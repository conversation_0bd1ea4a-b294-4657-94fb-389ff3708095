import React from 'react';
import {
    List,
    Datagrid,
    TextField,
    EmailField,
    DateField,
    BooleanField,
    SelectField,
    EditButton,
    ShowButton,
    DeleteButton,
    TopToolbar,
    CreateButton,
    ExportButton,
    FilterButton,
    SearchInput,
    SelectInput,
    BooleanInput,
} from 'react-admin';
import { Chip, Box, Alert } from '@mui/material';
import { User } from '../types';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { PermissionUtils } from '../utils/permissions';

const UserFilters = [
    <SearchInput source="search" placeholder="Search users..." alwaysOn />,
    <SelectInput
        source="role"
        choices={[
            { id: 'admin', name: 'Admin' },
            { id: 'manager', name: 'Manager' },
            { id: 'employee', name: 'Employee' },
        ]}
        emptyText="All roles"
    />,
    <BooleanInput source="is_active" label="Active only" />,
];

const RoleField = ({ record }: { record?: User }) => {
    if (!record) return null;

    return (
        <Chip
            label={PermissionUtils.getRoleDisplayName(record.role)}
            color={PermissionUtils.getRoleColor(record.role)}
            size="small"
            variant="outlined"
        />
    );
};

const UserListActions = () => {
    const { canCreateUser } = useUserPermissions();

    return (
        <TopToolbar>
            <FilterButton />
            <ExportButton />
            {canCreateUser() && <CreateButton />}
        </TopToolbar>
    );
};

const UserBulkActionButtons = () => {
    const { permissions } = useUserPermissions();

    // Only admins can perform bulk actions
    if (permissions !== 'admin') {
        return null;
    }

    return (
        <>
            {/* Add bulk actions here if needed */}
        </>
    );
};

const UserRowActions = ({ record }: { record?: User }) => {
    const { canViewUser, canEditUser, canDeleteUser } = useUserPermissions();

    if (!record) return null;

    const canView = canViewUser(record);
    const canEdit = canEditUser(record);
    const canDelete = canDeleteUser(record);

    return (
        <Box sx={{ display: 'flex', gap: 1 }}>
            {canView && <ShowButton record={record} />}
            {canEdit && <EditButton record={record} />}
            {canDelete && <DeleteButton record={record} />}
        </Box>
    );
};

export const UserList = () => {
    const { canAccessUserManagement, isLoading, getPermissionDeniedMessage } = useUserPermissions();

    if (isLoading) {
        return <div>Loading...</div>;
    }

    // Only admin and manager can access user list
    if (!canAccessUserManagement()) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">
                    {getPermissionDeniedMessage('view user list')}
                </Alert>
            </Box>
        );
    }

    return (
        <List
            filters={UserFilters}
            actions={<UserListActions />}
            perPage={25}
            sort={{ field: 'created_at', order: 'DESC' }}
        >
            <Datagrid
                bulkActionButtons={<UserBulkActionButtons />}
                rowClick="show"
            >
                <TextField source="username" label="Username" />
                <EmailField source="email" label="Email" />
                <RoleField source="role" label="Role" />
                <BooleanField source="is_active" label="Active" />
                <DateField source="last_login" label="Last Login" showTime />
                <DateField source="created_at" label="Created" showTime />
                <UserRowActions />
            </Datagrid>
        </List>
    );
};

export default UserList;
