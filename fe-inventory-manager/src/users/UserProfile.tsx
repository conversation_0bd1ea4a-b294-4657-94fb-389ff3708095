import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    CardContent,
    Ty<PERSON>graphy,
    TextField,
    Button,
    Box,
    Alert,
    CircularProgress,
    Avatar,
    Chip,
    Grid,
} from '@mui/material';
import { useNotify, useGetIdentity, useDataProvider } from 'react-admin';
import { Link } from 'react-router-dom';
import { User } from '../types';
import PersonIcon from '@mui/icons-material/Person';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import { PermissionUtils } from '../utils/permissions';

export const UserProfile = () => {
    const { identity, isLoading: identityLoading } = useGetIdentity();
    const notify = useNotify();
    const dataProvider = useDataProvider();
    
    const [user, setUser] = useState<User | null>(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [formData, setFormData] = useState({
        username: '',
        email: '',
    });
    const [errors, setErrors] = useState<{ username?: string; email?: string }>({});

    useEffect(() => {
        if (identity?.id && !identityLoading) {
            loadUserData();
        }
    }, [identity, identityLoading]);

    const loadUserData = async () => {
        try {
            setLoading(true);
            const { data } = await dataProvider.getOne('users', { id: identity.id });
            setUser(data);
            setFormData({
                username: data.username,
                email: data.email,
            });
        } catch (error: any) {
            console.error('Failed to load user data:', error);
            notify('Failed to load profile data', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    const validateForm = (): boolean => {
        const newErrors: { username?: string; email?: string } = {};

        if (!formData.username || formData.username.length < 3) {
            newErrors.username = 'Username must be at least 3 characters long';
        }

        if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm() || !user) {
            return;
        }

        setSaving(true);
        
        try {
            await dataProvider.update('users', {
                id: user.id,
                data: formData,
                previousData: user,
            });
            
            notify('Profile updated successfully', { type: 'success' });
            loadUserData(); // Reload to get updated data
        } catch (error: any) {
            console.error('Profile update error:', error);
            notify(error?.body?.message || 'Failed to update profile', { type: 'error' });
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (field: 'username' | 'email') => (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value,
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined,
            }));
        }
    };

    const getRoleConfig = (role: string) => {
        const configs = {
            admin: {
                icon: <AdminPanelSettingsIcon fontSize="small" />,
            },
            manager: {
                icon: <ManageAccountsIcon fontSize="small" />,
            },
            employee: {
                icon: <PersonIcon fontSize="small" />,
            },
        };
        return configs[role as keyof typeof configs] || configs.employee;
    };

    const getInitials = (username: string) => {
        return username.substring(0, 2).toUpperCase();
    };

    if (identityLoading || loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
            </Box>
        );
    }

    if (!user) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">
                    Failed to load profile data. Please try again.
                </Alert>
            </Box>
        );
    }

    const roleConfig = getRoleConfig(user.role);

    return (
        <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
            <Typography variant="h4" gutterBottom>
                My Profile
            </Typography>
            
            <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                    <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                            <Avatar
                                sx={{ 
                                    width: 100, 
                                    height: 100, 
                                    fontSize: '2rem',
                                    bgcolor: 'primary.main',
                                    mx: 'auto',
                                    mb: 2
                                }}
                            >
                                {getInitials(user.username)}
                            </Avatar>
                            
                            <Typography variant="h5" gutterBottom>
                                {user.username}
                            </Typography>
                            
                            <Chip
                                icon={roleConfig.icon}
                                label={PermissionUtils.getRoleDisplayName(user.role)}
                                color={PermissionUtils.getRoleColor(user.role)}
                                variant="outlined"
                                sx={{ mb: 2 }}
                            />
                            
                            <Box>
                                <Chip
                                    label={user.is_active ? 'Active' : 'Inactive'}
                                    color={user.is_active ? 'success' : 'default'}
                                    variant={user.is_active ? 'filled' : 'outlined'}
                                    size="small"
                                />
                            </Box>
                            
                            <Button
                                component={Link}
                                to={`/users/${user.id}/password`}
                                variant="outlined"
                                fullWidth
                                sx={{ mt: 2 }}
                            >
                                Change Password
                            </Button>
                        </CardContent>
                    </Card>
                </Grid>
                
                <Grid item xs={12} md={8}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Profile Information
                            </Typography>
                            
                            <form onSubmit={handleSubmit}>
                                <TextField
                                    fullWidth
                                    label="Username"
                                    value={formData.username}
                                    onChange={handleInputChange('username')}
                                    error={!!errors.username}
                                    helperText={errors.username || '3-50 characters, letters and numbers only'}
                                    margin="normal"
                                    required
                                />

                                <TextField
                                    fullWidth
                                    label="Email"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange('email')}
                                    error={!!errors.email}
                                    helperText={errors.email}
                                    margin="normal"
                                    required
                                />

                                <TextField
                                    fullWidth
                                    label="User ID"
                                    value={user.id}
                                    margin="normal"
                                    disabled
                                    helperText="This is your unique user identifier"
                                />

                                <TextField
                                    fullWidth
                                    label="Last Login"
                                    value={user.last_login ? new Date(user.last_login).toLocaleString() : 'Never'}
                                    margin="normal"
                                    disabled
                                />

                                <TextField
                                    fullWidth
                                    label="Account Created"
                                    value={new Date(user.created_at).toLocaleString()}
                                    margin="normal"
                                    disabled
                                />

                                <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                                    <Button
                                        type="submit"
                                        variant="contained"
                                        disabled={saving}
                                        startIcon={saving && <CircularProgress size={20} />}
                                    >
                                        {saving ? 'Saving...' : 'Save Changes'}
                                    </Button>
                                </Box>
                            </form>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );
};

export default UserProfile;
