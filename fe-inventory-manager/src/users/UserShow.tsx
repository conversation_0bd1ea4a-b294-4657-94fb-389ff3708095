import React from 'react';
import {
    Show,
    SimpleShowLayout,
    TextField,
    EmailField,
    DateField,
    BooleanField,
    TopToolbar,
    EditButton,
    ListButton,
    DeleteButton,
    useRecordContext,
} from 'react-admin';
import {
    Box,
    Typography,
    Chip,
    Card,
    CardContent,
    Grid,
    Alert,
    Button,
    Avatar
} from '@mui/material';
import { Link } from 'react-router-dom';
import { User } from '../types';
import PersonIcon from '@mui/icons-material/Person';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import { useUserPermissions, useUserPermissionsForTarget } from '../hooks/useUserPermissions';
import { PermissionUtils } from '../utils/permissions';

const UserShowActions = () => {
    const record = useRecordContext<User>();
    const { canEdit, canDelete } = useUserPermissionsForTarget(record);

    if (!record) return null;

    return (
        <TopToolbar>
            <ListButton />
            {canEdit && <EditButton />}
            {canDelete && <DeleteButton />}
        </TopToolbar>
    );
};

const RoleChip = ({ record }: { record?: User }) => {
    if (!record) return null;

    const roleConfig = {
        admin: {
            icon: <AdminPanelSettingsIcon fontSize="small" />,
        },
        manager: {
            icon: <ManageAccountsIcon fontSize="small" />,
        },
        employee: {
            icon: <PersonIcon fontSize="small" />,
        },
    };

    const config = roleConfig[record.role];

    return (
        <Chip
            icon={config.icon}
            label={PermissionUtils.getRoleDisplayName(record.role)}
            color={PermissionUtils.getRoleColor(record.role)}
            variant="outlined"
            size="medium"
        />
    );
};

const StatusChip = ({ record }: { record?: User }) => {
    if (!record) return null;
    
    return (
        <Chip
            label={record.is_active ? 'Active' : 'Inactive'}
            color={record.is_active ? 'success' : 'default'}
            variant={record.is_active ? 'filled' : 'outlined'}
            size="medium"
        />
    );
};

const UserAvatar = ({ record }: { record?: User }) => {
    if (!record) return null;
    
    const getInitials = (username: string) => {
        return username.substring(0, 2).toUpperCase();
    };
    
    return (
        <Avatar
            sx={{ 
                width: 80, 
                height: 80, 
                fontSize: '1.5rem',
                bgcolor: 'primary.main'
            }}
        >
            {getInitials(record.username)}
        </Avatar>
    );
};

const PasswordChangeButton = () => {
    const record = useRecordContext<User>();
    const { canChangePassword } = useUserPermissionsForTarget(record);

    if (!record || !canChangePassword) return null;

    return (
        <Button
            component={Link}
            to={`/users/${record.id}/password`}
            variant="outlined"
            color="primary"
            sx={{ mt: 2 }}
        >
            Change Password
        </Button>
    );
};

const UserShowLayout = () => {
    const record = useRecordContext<User>();
    const { getPermissionDeniedMessage } = useUserPermissions();
    const { canView } = useUserPermissionsForTarget(record);

    if (!record) return null;

    if (!canView) {
        return (
            <Alert severity="error">
                {getPermissionDeniedMessage('view this user')}
            </Alert>
        );
    }
    
    return (
        <Box sx={{ p: 2 }}>
            <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                    <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                            <UserAvatar record={record} />
                            <Typography variant="h5" sx={{ mt: 2, mb: 1 }}>
                                {record.username}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', mb: 2 }}>
                                <RoleChip record={record} />
                                <StatusChip record={record} />
                            </Box>
                            <PasswordChangeButton />
                        </CardContent>
                    </Card>
                </Grid>
                
                <Grid item xs={12} md={8}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                User Details
                            </Typography>
                            <SimpleShowLayout>
                                <TextField source="id" label="User ID" />
                                <TextField source="username" label="Username" />
                                <EmailField source="email" label="Email" />
                                <DateField source="last_login" label="Last Login" showTime />
                                <DateField source="created_at" label="Created" showTime />
                                <DateField source="updated_at" label="Last Updated" showTime />
                            </SimpleShowLayout>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );
};

export const UserShow = () => {
    return (
        <Show actions={<UserShowActions />} title="User Details">
            <UserShowLayout />
        </Show>
    );
};

export default UserShow;
