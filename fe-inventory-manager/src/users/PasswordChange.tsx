import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
    Card,
    CardContent,
    Typography,
    TextField,
    Button,
    Box,
    Alert,
    CircularProgress,
    IconButton,
    InputAdornment,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useNotify, useDataProvider } from 'react-admin';
import { PasswordChangeData, User } from '../types';
import { useUserPermissions } from '../hooks/useUserPermissions';

export const PasswordChange = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const notify = useNotify();
    const dataProvider = useDataProvider();
    const { userId, getPermissionDeniedMessage, isLoading: permissionsLoading } = useUserPermissions();

    const [targetUser, setTargetUser] = useState<User | null>(null);
    const [formData, setFormData] = useState<PasswordChangeData>({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
    });
    const [loading, setLoading] = useState(false);
    const [loadingUser, setLoadingUser] = useState(true);
    const [errors, setErrors] = useState<Partial<PasswordChangeData>>({});
    const [showPasswords, setShowPasswords] = useState({
        current: false,
        new: false,
        confirm: false,
    });

    // Load target user data
    useEffect(() => {
        if (id && !permissionsLoading) {
            loadTargetUser();
        }
    }, [id, permissionsLoading]);

    const loadTargetUser = async () => {
        try {
            setLoadingUser(true);
            const { data } = await dataProvider.getOne('users', { id: id! });
            setTargetUser(data);
        } catch (error: any) {
            console.error('Failed to load user:', error);
            notify('Failed to load user data', { type: 'error' });
            navigate('/users');
        } finally {
            setLoadingUser(false);
        }
    };

    // Check permissions
    const canChangePassword = targetUser ?
        (id === userId || targetUser.role !== 'admin' || userId === targetUser.id) : false;

    if (permissionsLoading || loadingUser) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
            </Box>
        );
    }

    if (!targetUser || !canChangePassword) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">
                    {getPermissionDeniedMessage('change this password')}
                </Alert>
            </Box>
        );
    }

    const validateForm = (): boolean => {
        const newErrors: Partial<PasswordChangeData> = {};

        if (!formData.currentPassword) {
            newErrors.currentPassword = 'Current password is required';
        }

        if (!formData.newPassword) {
            newErrors.newPassword = 'New password is required';
        } else if (formData.newPassword.length < 8) {
            newErrors.newPassword = 'Password must be at least 8 characters long';
        }

        if (!formData.confirmPassword) {
            newErrors.confirmPassword = 'Please confirm your new password';
        } else if (formData.newPassword !== formData.confirmPassword) {
            newErrors.confirmPassword = 'Passwords do not match';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLoading(true);
        
        try {
            // Use custom HTTP client to call password change endpoint
            const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000';
            const token = localStorage.getItem('token');
            
            const response = await fetch(`${apiUrl}/api/users/${id}/password`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`,
                },
                body: JSON.stringify({
                    currentPassword: formData.currentPassword,
                    newPassword: formData.newPassword,
                }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'Failed to change password');
            }

            notify('Password changed successfully', { type: 'success' });
            navigate(`/users/${id}/show`);
        } catch (error: any) {
            console.error('Password change error:', error);
            notify(error.message || 'Failed to change password', { type: 'error' });
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field: keyof PasswordChangeData) => (
        e: React.ChangeEvent<HTMLInputElement>
    ) => {
        setFormData(prev => ({
            ...prev,
            [field]: e.target.value,
        }));
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined,
            }));
        }
    };

    const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
        setShowPasswords(prev => ({
            ...prev,
            [field]: !prev[field],
        }));
    };

    return (
        <Box sx={{ maxWidth: 600, mx: 'auto', p: 3 }}>
            <Card>
                <CardContent>
                    <Typography variant="h5" gutterBottom>
                        Change Password
                    </Typography>
                    
                    <Alert severity="info" sx={{ mb: 3 }}>
                        Password must be at least 8 characters long and contain a mix of letters, numbers, and special characters.
                    </Alert>

                    <form onSubmit={handleSubmit}>
                        <TextField
                            fullWidth
                            label="Current Password"
                            type={showPasswords.current ? 'text' : 'password'}
                            value={formData.currentPassword}
                            onChange={handleInputChange('currentPassword')}
                            error={!!errors.currentPassword}
                            helperText={errors.currentPassword}
                            margin="normal"
                            required
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            onClick={() => togglePasswordVisibility('current')}
                                            edge="end"
                                        >
                                            {showPasswords.current ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <TextField
                            fullWidth
                            label="New Password"
                            type={showPasswords.new ? 'text' : 'password'}
                            value={formData.newPassword}
                            onChange={handleInputChange('newPassword')}
                            error={!!errors.newPassword}
                            helperText={errors.newPassword}
                            margin="normal"
                            required
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            onClick={() => togglePasswordVisibility('new')}
                                            edge="end"
                                        >
                                            {showPasswords.new ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <TextField
                            fullWidth
                            label="Confirm New Password"
                            type={showPasswords.confirm ? 'text' : 'password'}
                            value={formData.confirmPassword}
                            onChange={handleInputChange('confirmPassword')}
                            error={!!errors.confirmPassword}
                            helperText={errors.confirmPassword}
                            margin="normal"
                            required
                            InputProps={{
                                endAdornment: (
                                    <InputAdornment position="end">
                                        <IconButton
                                            onClick={() => togglePasswordVisibility('confirm')}
                                            edge="end"
                                        >
                                            {showPasswords.confirm ? <VisibilityOff /> : <Visibility />}
                                        </IconButton>
                                    </InputAdornment>
                                ),
                            }}
                        />

                        <Box sx={{ mt: 3, display: 'flex', gap: 2 }}>
                            <Button
                                type="submit"
                                variant="contained"
                                disabled={loading}
                                startIcon={loading && <CircularProgress size={20} />}
                            >
                                {loading ? 'Changing Password...' : 'Change Password'}
                            </Button>
                            
                            <Button
                                variant="outlined"
                                onClick={() => navigate(`/users/${id}/show`)}
                                disabled={loading}
                            >
                                Cancel
                            </Button>
                        </Box>
                    </form>
                </CardContent>
            </Card>
        </Box>
    );
};

export default PasswordChange;
