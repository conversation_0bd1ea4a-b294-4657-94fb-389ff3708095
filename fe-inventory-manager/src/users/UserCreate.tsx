import React from 'react';
import {
    Create,
    SimpleForm,
    TextInput,
    SelectInput,
    BooleanInput,
    PasswordInput,
    required,
    email,
    minLength,
    useNotify,
    useRedirect,
} from 'react-admin';
import { Box, Typography, Alert } from '@mui/material';
import { UserFormData } from '../types';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { PermissionUtils } from '../utils/permissions';

const validateUsername = [required(), minLength(3)];
const validateEmail = [required(), email()];
const validatePassword = [required(), minLength(8)];

const validatePasswordMatch = (value: string, allValues: UserFormData) => {
    if (value !== allValues.password) {
        return 'Passwords do not match';
    }
    return undefined;
};

const UserCreateForm = () => {
    const { permissions, getAllowedRolesToAssign } = useUserPermissions();
    const notify = useNotify();
    const redirect = useRedirect();
    
    const transform = (data: UserFormData) => {
        // Remove confirmPassword before sending to backend
        const { confirmPassword, ...userData } = data;
        return userData;
    };

    const onSuccess = () => {
        notify('User created successfully', { type: 'success' });
        redirect('list', 'users');
    };

    const onError = (error: any) => {
        console.error('User creation error:', error);
        notify(
            error?.body?.message || 'Failed to create user',
            { type: 'error' }
        );
    };

    return (
        <SimpleForm
            transform={transform}
            onSuccess={onSuccess}
            onError={onError}
        >
            <Typography variant="h6" gutterBottom>
                User Information
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                <TextInput
                    source="username"
                    label="Username"
                    validate={validateUsername}
                    helperText="3-50 characters, letters and numbers only"
                    sx={{ flex: 1 }}
                />
                <TextInput
                    source="email"
                    label="Email"
                    validate={validateEmail}
                    sx={{ flex: 1 }}
                />
            </Box>

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Password
            </Typography>
            
            <Alert severity="info" sx={{ mb: 2 }}>
                Password must be at least 8 characters long and contain a mix of letters, numbers, and special characters.
            </Alert>
            
            <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                <PasswordInput
                    source="password"
                    label="Password"
                    validate={validatePassword}
                    sx={{ flex: 1 }}
                />
                <PasswordInput
                    source="confirmPassword"
                    label="Confirm Password"
                    validate={[required(), validatePasswordMatch]}
                    sx={{ flex: 1 }}
                />
            </Box>

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                Role & Status
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, width: '100%', alignItems: 'center' }}>
                <SelectInput
                    source="role"
                    label="Role"
                    choices={getAllowedRolesToAssign().map(role => ({
                        id: role,
                        name: PermissionUtils.getRoleDisplayName(role)
                    }))}
                    defaultValue="employee"
                    validate={required()}
                    sx={{ flex: 1 }}
                />
                <BooleanInput
                    source="is_active"
                    label="Active"
                    defaultValue={true}
                />
            </Box>
        </SimpleForm>
    );
};

export const UserCreate = () => {
    const { canCreateUser, getPermissionDeniedMessage, isLoading } = useUserPermissions();

    if (isLoading) {
        return <div>Loading...</div>;
    }

    // Only admins can create users
    if (!canCreateUser()) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">
                    {getPermissionDeniedMessage('create users')}
                </Alert>
            </Box>
        );
    }

    return (
        <Create title="Create New User">
            <UserCreateForm />
        </Create>
    );
};

export default UserCreate;
