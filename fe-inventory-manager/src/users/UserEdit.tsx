import React from 'react';
import {
    Edit,
    SimpleForm,
    TextInput,
    SelectInput,
    BooleanInput,
    required,
    email,
    minLength,
    useRecordContext,
    useNotify,
    useRedirect,
    TopToolbar,
    ShowButton,
    ListButton,
} from 'react-admin';
import { Box, Typography, Al<PERSON>, Button } from '@mui/material';
import { Link } from 'react-router-dom';
import { User } from '../types';
import { useUserPermissions, useUserPermissionsForTarget } from '../hooks/useUserPermissions';
import { PermissionUtils } from '../utils/permissions';

const validateUsername = [required(), minLength(3)];
const validateEmail = [required(), email()];

const UserEditActions = () => (
    <TopToolbar>
        <ShowButton />
        <ListButton />
    </TopToolbar>
);

const PasswordChangeButton = () => {
    const record = useRecordContext<User>();
    const { canChangePassword } = useUserPermissionsForTarget(record);

    if (!record || !canChangePassword) return null;

    return (
        <Button
            component={Link}
            to={`/users/${record.id}/password`}
            variant="outlined"
            color="primary"
            sx={{ mt: 2 }}
        >
            Change Password
        </Button>
    );
};

const UserEditForm = () => {
    const record = useRecordContext<User>();
    const { getAllowedRolesToAssign, userId, getPermissionDeniedMessage } = useUserPermissions();
    const { canEdit, canChangeRole, canChangeStatus } = useUserPermissionsForTarget(record);
    const notify = useNotify();
    const redirect = useRedirect();

    if (!record) return null;

    // Check if user can edit this record
    if (!canEdit) {
        return (
            <Alert severity="error">
                {getPermissionDeniedMessage('edit this user')}
            </Alert>
        );
    }

    const isOwnRecord = record.id === userId;
    
    const onSuccess = () => {
        notify('User updated successfully', { type: 'success' });
        redirect('show', 'users', record.id);
    };

    const onError = (error: any) => {
        console.error('User update error:', error);
        notify(
            error?.body?.message || 'Failed to update user',
            { type: 'error' }
        );
    };

    return (
        <SimpleForm onSuccess={onSuccess} onError={onError}>
            <Typography variant="h6" gutterBottom>
                User Information
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                <TextInput
                    source="username"
                    label="Username"
                    validate={validateUsername}
                    helperText="3-50 characters, letters and numbers only"
                    sx={{ flex: 1 }}
                />
                <TextInput
                    source="email"
                    label="Email"
                    validate={validateEmail}
                    sx={{ flex: 1 }}
                />
            </Box>

            {(canChangeRole || canChangeStatus) && (
                <>
                    <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                        Role & Status
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2, width: '100%', alignItems: 'center' }}>
                        {canChangeRole && (
                            <SelectInput
                                source="role"
                                label="Role"
                                choices={getAllowedRolesToAssign().map(role => ({
                                    id: role,
                                    name: PermissionUtils.getRoleDisplayName(role)
                                }))}
                                validate={required()}
                                sx={{ flex: 1 }}
                            />
                        )}
                        {canChangeStatus && (
                            <BooleanInput
                                source="is_active"
                                label="Active"
                            />
                        )}
                    </Box>
                </>
            )}

            {isOwnRecord && (
                <Alert severity="info" sx={{ mt: 2 }}>
                    You are editing your own profile. Role and status changes require administrator privileges.
                </Alert>
            )}

            <PasswordChangeButton />
        </SimpleForm>
    );
};

export const UserEdit = () => {
    return (
        <Edit actions={<UserEditActions />} title="Edit User">
            <UserEditForm />
        </Edit>
    );
};

export default UserEdit;
