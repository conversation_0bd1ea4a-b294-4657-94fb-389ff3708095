import { User } from '../types';

export type UserRole = 'admin' | 'manager' | 'employee';

export interface PermissionContext {
    userRole: UserRole;
    userId: string;
    targetUser?: User;
    targetUserId?: string;
}

/**
 * Permission utility class for role-based access control
 */
export class UserPermissions {
    private userRole: UserRole;
    private userId: string;

    constructor(userRole: UserRole, userId: string) {
        this.userRole = userRole;
        this.userId = userId;
    }

    /**
     * Check if user can view the user list
     */
    canViewUserList(): boolean {
        return ['admin', 'manager'].includes(this.userRole);
    }

    /**
     * Check if user can create new users
     */
    canCreateUser(): boolean {
        return this.userRole === 'admin';
    }

    /**
     * Check if user can view a specific user
     */
    canViewUser(targetUser: User): boolean {
        // Admin can view all users
        if (this.userRole === 'admin') return true;
        
        // Manager can view non-admin users
        if (this.userRole === 'manager') {
            return targetUser.role !== 'admin';
        }
        
        // Employee can only view their own profile
        return this.userId === targetUser.id;
    }

    /**
     * Check if user can edit a specific user
     */
    canEditUser(targetUser: User): boolean {
        // Admin can edit all users except themselves for certain fields
        if (this.userRole === 'admin') return true;
        
        // Manager can edit employee users only
        if (this.userRole === 'manager') {
            return targetUser.role === 'employee';
        }
        
        // Employee can only edit their own profile
        return this.userId === targetUser.id;
    }

    /**
     * Check if user can delete/deactivate a specific user
     */
    canDeleteUser(targetUser: User): boolean {
        // Only admin can delete users, but not themselves
        return this.userRole === 'admin' && this.userId !== targetUser.id;
    }

    /**
     * Check if user can change role of a specific user
     */
    canChangeUserRole(targetUser: User): boolean {
        // Only admin can change roles, and not their own
        return this.userRole === 'admin' && this.userId !== targetUser.id;
    }

    /**
     * Check if user can change status (active/inactive) of a specific user
     */
    canChangeUserStatus(targetUser: User): boolean {
        // Only admin can change status, and not their own
        return this.userRole === 'admin' && this.userId !== targetUser.id;
    }

    /**
     * Check if user can change password of a specific user
     */
    canChangeUserPassword(targetUser: User): boolean {
        // Admin can change any password, users can change their own
        return this.userRole === 'admin' || this.userId === targetUser.id;
    }

    /**
     * Get allowed roles that this user can assign to others
     */
    getAllowedRolesToAssign(): UserRole[] {
        if (this.userRole === 'admin') {
            return ['admin', 'manager', 'employee'];
        }
        return []; // Only admin can assign roles
    }

    /**
     * Filter users based on what this user is allowed to see
     */
    filterAllowedUsers(users: User[]): User[] {
        if (this.userRole === 'admin') {
            return users; // Admin can see all users
        }
        
        if (this.userRole === 'manager') {
            // Manager can see non-admin users
            return users.filter(user => user.role !== 'admin');
        }
        
        // Employee can only see themselves
        return users.filter(user => user.id === this.userId);
    }

    /**
     * Check if user can access user management section
     */
    canAccessUserManagement(): boolean {
        return ['admin', 'manager'].includes(this.userRole);
    }

    /**
     * Check if user can view containers
     */
    canViewContainers(): boolean {
        return true; // All authenticated users can view containers
    }

    /**
     * Check if user can create containers
     */
    canCreateContainer(): boolean {
        return ['admin', 'manager'].includes(this.userRole);
    }

    /**
     * Check if user can edit containers
     */
    canEditContainer(): boolean {
        return ['admin', 'manager'].includes(this.userRole);
    }

    /**
     * Check if user can delete containers
     */
    canDeleteContainer(): boolean {
        return this.userRole === 'admin';
    }

    /**
     * Check if user can perform bulk operations on containers
     */
    canBulkUpdateContainers(): boolean {
        return ['admin', 'manager'].includes(this.userRole);
    }

    /**
     * Check if user can import inventory items to containers
     */
    canImportInventoryItems(): boolean {
        return ['admin', 'manager'].includes(this.userRole);
    }

    /**
     * Get error message for permission denial
     */
    getPermissionDeniedMessage(action: string): string {
        const roleMessages = {
            admin: 'Administrator privileges required for this action.',
            manager: 'Manager or administrator privileges required for this action.',
            employee: 'You can only manage your own profile.'
        };

        return roleMessages[this.userRole] || 'Access denied.';
    }
}

/**
 * Hook-like function to create permission checker
 */
export const createPermissionChecker = (userRole: UserRole, userId: string): UserPermissions => {
    return new UserPermissions(userRole, userId);
};

/**
 * Utility functions for common permission checks
 */
export const PermissionUtils = {
    /**
     * Check if a role has higher or equal privileges than another
     */
    hasHigherOrEqualPrivileges(userRole: UserRole, targetRole: UserRole): boolean {
        const roleHierarchy = { admin: 3, manager: 2, employee: 1 };
        return roleHierarchy[userRole] >= roleHierarchy[targetRole];
    },

    /**
     * Check if a role can manage another role
     */
    canManageRole(userRole: UserRole, targetRole: UserRole): boolean {
        if (userRole === 'admin') return true;
        if (userRole === 'manager') return targetRole === 'employee';
        return false;
    },

    /**
     * Get user-friendly role name
     */
    getRoleDisplayName(role: UserRole): string {
        const roleNames = {
            admin: 'Administrator',
            manager: 'Manager',
            employee: 'Employee'
        };
        return roleNames[role] || role;
    },

    /**
     * Get role color for UI components
     */
    getRoleColor(role: UserRole): 'error' | 'warning' | 'primary' {
        const roleColors = {
            admin: 'error' as const,
            manager: 'warning' as const,
            employee: 'primary' as const
        };
        return roleColors[role] || 'primary';
    }
};

export default UserPermissions;
