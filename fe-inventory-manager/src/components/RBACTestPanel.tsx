import React, { useState } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON>po<PERSON>,
    <PERSON>,
    <PERSON><PERSON>,
    Al<PERSON>,
    <PERSON>,
    Grid,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import { useUserPermissions } from '../hooks/useUserPermissions';
import { PermissionUtils } from '../utils/permissions';
import { User } from '../types';

// Mock users for testing
const mockUsers: User[] = [
    {
        id: 'admin-1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
    },
    {
        id: 'manager-1',
        username: 'manager',
        email: '<EMAIL>',
        role: 'manager',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
    },
    {
        id: 'employee-1',
        username: 'employee',
        email: '<EMAIL>',
        role: 'employee',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
    },
];

interface PermissionTest {
    name: string;
    test: () => boolean;
    description: string;
}

export const RBACTestPanel: React.FC = () => {
    const {
        permissions,
        userId,
        canViewUserList,
        canCreateUser,
        canAccessUserManagement,
        getAllowedRolesToAssign,
        filterAllowedUsers,
        isLoading,
    } = useUserPermissions();

    const [selectedUser, setSelectedUser] = useState<User>(mockUsers[0]);

    if (isLoading) {
        return <div>Loading permissions...</div>;
    }

    const permissionTests: PermissionTest[] = [
        {
            name: 'Can View User List',
            test: () => canViewUserList(),
            description: 'Admin and Manager should be able to view user list',
        },
        {
            name: 'Can Create User',
            test: () => canCreateUser(),
            description: 'Only Admin should be able to create users',
        },
        {
            name: 'Can Access User Management',
            test: () => canAccessUserManagement(),
            description: 'Admin and Manager should have access to user management',
        },
        {
            name: 'Can View Admin User',
            test: () => {
                const adminUser = mockUsers.find(u => u.role === 'admin')!;
                return permissions === 'admin' || 
                       (permissions === 'manager' && false) || // Managers cannot view admins
                       (permissions === 'employee' && userId === adminUser.id);
            },
            description: 'Only Admin can view admin users, Manager cannot, Employee only if it\'s themselves',
        },
        {
            name: 'Can Edit Employee User',
            test: () => {
                const employeeUser = mockUsers.find(u => u.role === 'employee')!;
                return permissions === 'admin' || 
                       permissions === 'manager' || 
                       (permissions === 'employee' && userId === employeeUser.id);
            },
            description: 'Admin and Manager can edit employee users, Employee only themselves',
        },
        {
            name: 'Can Delete User',
            test: () => {
                const testUser = mockUsers.find(u => u.id !== userId)!;
                return permissions === 'admin' && userId !== testUser.id;
            },
            description: 'Only Admin can delete users, but not themselves',
        },
    ];

    const filteredUsers = filterAllowedUsers(mockUsers);
    const allowedRoles = getAllowedRolesToAssign();

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                RBAC Test Panel
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
                Current User: <strong>{permissions}</strong> (ID: {userId})
            </Alert>

            <Grid container spacing={3}>
                {/* Current Permissions */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Current Permissions
                            </Typography>
                            
                            <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2">Role:</Typography>
                                <Chip
                                    label={PermissionUtils.getRoleDisplayName(permissions)}
                                    color={PermissionUtils.getRoleColor(permissions)}
                                    size="small"
                                />
                            </Box>

                            <Box sx={{ mb: 2 }}>
                                <Typography variant="subtitle2">Allowed Roles to Assign:</Typography>
                                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                                    {allowedRoles.length > 0 ? (
                                        allowedRoles.map(role => (
                                            <Chip
                                                key={role}
                                                label={PermissionUtils.getRoleDisplayName(role)}
                                                color={PermissionUtils.getRoleColor(role)}
                                                size="small"
                                                variant="outlined"
                                            />
                                        ))
                                    ) : (
                                        <Typography variant="body2" color="text.secondary">
                                            None
                                        </Typography>
                                    )}
                                </Box>
                            </Box>

                            <Box>
                                <Typography variant="subtitle2">Visible Users:</Typography>
                                <Typography variant="body2" color="text.secondary">
                                    {filteredUsers.length} out of {mockUsers.length} users
                                </Typography>
                                <List dense>
                                    {filteredUsers.map(user => (
                                        <ListItem key={user.id} sx={{ py: 0 }}>
                                            <ListItemText
                                                primary={user.username}
                                                secondary={PermissionUtils.getRoleDisplayName(user.role)}
                                            />
                                        </ListItem>
                                    ))}
                                </List>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Permission Tests */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Permission Tests
                            </Typography>
                            
                            <List>
                                {permissionTests.map((test, index) => {
                                    const result = test.test();
                                    return (
                                        <ListItem key={index} sx={{ px: 0 }}>
                                            <ListItemIcon>
                                                {result ? (
                                                    <CheckCircleIcon color="success" />
                                                ) : (
                                                    <CancelIcon color="error" />
                                                )}
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={test.name}
                                                secondary={test.description}
                                            />
                                        </ListItem>
                                    );
                                })}
                            </List>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Role Simulation */}
                <Grid item xs={12}>
                    <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography variant="h6">
                                Role-Based Access Simulation
                            </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                This section shows what different roles can see and do. 
                                Note: This is a simulation based on the current user's perspective.
                            </Typography>
                            
                            <Grid container spacing={2}>
                                {['admin', 'manager', 'employee'].map(role => (
                                    <Grid item xs={12} md={4} key={role}>
                                        <Card variant="outlined">
                                            <CardContent>
                                                <Typography variant="h6" gutterBottom>
                                                    {PermissionUtils.getRoleDisplayName(role as any)}
                                                </Typography>
                                                
                                                <List dense>
                                                    <ListItem sx={{ px: 0 }}>
                                                        <ListItemIcon>
                                                            {role !== 'employee' ? (
                                                                <CheckCircleIcon color="success" fontSize="small" />
                                                            ) : (
                                                                <CancelIcon color="error" fontSize="small" />
                                                            )}
                                                        </ListItemIcon>
                                                        <ListItemText primary="View User List" />
                                                    </ListItem>
                                                    
                                                    <ListItem sx={{ px: 0 }}>
                                                        <ListItemIcon>
                                                            {role === 'admin' ? (
                                                                <CheckCircleIcon color="success" fontSize="small" />
                                                            ) : (
                                                                <CancelIcon color="error" fontSize="small" />
                                                            )}
                                                        </ListItemIcon>
                                                        <ListItemText primary="Create Users" />
                                                    </ListItem>
                                                    
                                                    <ListItem sx={{ px: 0 }}>
                                                        <ListItemIcon>
                                                            {role === 'admin' ? (
                                                                <CheckCircleIcon color="success" fontSize="small" />
                                                            ) : (
                                                                <CancelIcon color="error" fontSize="small" />
                                                            )}
                                                        </ListItemIcon>
                                                        <ListItemText primary="Delete Users" />
                                                    </ListItem>
                                                    
                                                    <ListItem sx={{ px: 0 }}>
                                                        <ListItemIcon>
                                                            {role !== 'employee' ? (
                                                                <CheckCircleIcon color="success" fontSize="small" />
                                                            ) : (
                                                                <CancelIcon color="error" fontSize="small" />
                                                            )}
                                                        </ListItemIcon>
                                                        <ListItemText 
                                                            primary="Edit Other Users" 
                                                            secondary={role === 'manager' ? 'Employees only' : ''}
                                                        />
                                                    </ListItem>
                                                </List>
                                            </CardContent>
                                        </Card>
                                    </Grid>
                                ))}
                            </Grid>
                        </AccordionDetails>
                    </Accordion>
                </Grid>
            </Grid>
        </Box>
    );
};

export default RBACTestPanel;
