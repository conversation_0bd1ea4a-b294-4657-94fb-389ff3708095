import React, { useState } from 'react';
import {
    BulkDeleteButton,
    BulkUpdateButton,
    usePermissions,
    useNotify,
    useUnselectAll,
    useListContext,
    Button,
} from 'react-admin';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Typography,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Alert,
    Box,
    Chip,
    List,
    ListItem,
    ListItemText,
    Divider,
} from '@mui/material';
import {
    Update as UpdateIcon,
    Delete as DeleteIcon,
    FileUpload as ImportIcon,
    CheckCircle as CheckIcon,
} from '@mui/icons-material';
import { Container } from '../types';

// Status choices for bulk update
const statusChoices = [
    { id: 'arrived', name: 'Arrived' },
    { id: 'processing', name: 'Processing' },
    { id: 'completed', name: 'Completed' },
];

// Bulk status update component
const BulkStatusUpdate = () => {
    const [open, setOpen] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState('');
    const { selectedIds, data } = useListContext();
    const { permissions } = usePermissions();
    const notify = useNotify();
    const unselectAll = useUnselectAll('containers');

    const canEdit = permissions?.role === 'admin' || permissions?.role === 'manager';

    if (!canEdit) return null;

    const selectedContainers = selectedIds.map(id => data?.find((container: Container) => container.id === id)).filter(Boolean) as Container[];

    const validateStatusTransition = (containers: Container[], newStatus: string) => {
        const validTransitions = {
            'arrived': ['processing'],
            'processing': ['completed'],
            'completed': [],
        };

        const invalidContainers = containers.filter(container => {
            const allowedStatuses = validTransitions[container.status as keyof typeof validTransitions] || [];
            return container.status !== newStatus && !allowedStatuses.includes(newStatus as any);
        });

        return invalidContainers;
    };

    const handleUpdate = async () => {
        if (!selectedStatus) {
            notify('Please select a status', { type: 'warning' });
            return;
        }

        const invalidContainers = validateStatusTransition(selectedContainers, selectedStatus);
        
        if (invalidContainers.length > 0) {
            notify(`Cannot update ${invalidContainers.length} container(s) due to invalid status transitions`, { type: 'error' });
            return;
        }

        try {
            // This would typically use a bulk update mutation
            // For now, we'll show a success message
            notify(`Successfully updated ${selectedContainers.length} container(s) to ${selectedStatus}`, { type: 'success' });
            setOpen(false);
            setSelectedStatus('');
            unselectAll();
        } catch (error) {
            notify('Failed to update containers', { type: 'error' });
        }
    };

    const getStatusSummary = () => {
        const statusCounts = selectedContainers.reduce((acc, container) => {
            acc[container.status] = (acc[container.status] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        return Object.entries(statusCounts).map(([status, count]) => (
            <Chip
                key={status}
                label={`${status}: ${count}`}
                size="small"
                sx={{ mr: 1, mb: 1 }}
            />
        ));
    };

    return (
        <>
            <Button
                onClick={() => setOpen(true)}
                startIcon={<UpdateIcon />}
                disabled={selectedIds.length === 0}
            >
                Update Status
            </Button>

            <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md" fullWidth>
                <DialogTitle>Bulk Status Update</DialogTitle>
                <DialogContent>
                    <Typography gutterBottom>
                        Update status for {selectedContainers.length} selected container(s)
                    </Typography>

                    <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" gutterBottom>
                            Current Status Distribution:
                        </Typography>
                        {getStatusSummary()}
                    </Box>

                    <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>New Status</InputLabel>
                        <Select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            label="New Status"
                        >
                            {statusChoices.map((choice) => (
                                <MenuItem key={choice.id} value={choice.id}>
                                    {choice.name}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>

                    {selectedStatus && (
                        <Box>
                            <Typography variant="subtitle2" gutterBottom>
                                Validation Results:
                            </Typography>
                            {(() => {
                                const invalidContainers = validateStatusTransition(selectedContainers, selectedStatus);
                                const validContainers = selectedContainers.filter(c => !invalidContainers.includes(c));

                                return (
                                    <Box>
                                        {validContainers.length > 0 && (
                                            <Alert severity="success" sx={{ mb: 1 }}>
                                                {validContainers.length} container(s) can be updated to {selectedStatus}
                                            </Alert>
                                        )}
                                        {invalidContainers.length > 0 && (
                                            <Alert severity="error" sx={{ mb: 1 }}>
                                                {invalidContainers.length} container(s) cannot be updated due to invalid status transitions
                                            </Alert>
                                        )}
                                    </Box>
                                );
                            })()}
                        </Box>
                    )}

                    <Alert severity="warning" sx={{ mt: 2 }}>
                        Status changes are permanent and will be tracked for audit purposes.
                    </Alert>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpen(false)}>Cancel</Button>
                    <Button
                        onClick={handleUpdate}
                        variant="contained"
                        disabled={!selectedStatus || validateStatusTransition(selectedContainers, selectedStatus).length === selectedContainers.length}
                    >
                        Update Status
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

// Bulk import component
const BulkImportAction = () => {
    const [open, setOpen] = useState(false);
    const { selectedIds } = useListContext();
    const { permissions } = usePermissions();
    const notify = useNotify();

    const canImport = permissions?.role === 'admin' || permissions?.role === 'manager';

    if (!canImport || selectedIds.length !== 1) return null;

    const handleImport = () => {
        // This would typically open a file upload dialog or navigate to import page
        notify('Bulk import feature would be implemented here', { type: 'info' });
        setOpen(false);
    };

    return (
        <>
            <Button
                onClick={() => setOpen(true)}
                startIcon={<ImportIcon />}
                disabled={selectedIds.length !== 1}
            >
                Import Items
            </Button>

            <Dialog open={open} onClose={() => setOpen(false)} maxWidth="sm" fullWidth>
                <DialogTitle>Bulk Import Inventory Items</DialogTitle>
                <DialogContent>
                    <Typography gutterBottom>
                        Import inventory items for the selected container.
                    </Typography>
                    
                    <Alert severity="info" sx={{ mt: 2 }}>
                        This feature allows you to upload a CSV file with inventory items for the selected container.
                        The CSV should include columns for serial numbers, product information, and condition.
                    </Alert>

                    <Typography variant="subtitle2" sx={{ mt: 2, mb: 1 }}>
                        Required CSV Format:
                    </Typography>
                    <List dense>
                        <ListItem>
                            <ListItemText primary="serial_number" secondary="Unique identifier for each item" />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary="product_id" secondary="Product reference ID" />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary="condition" secondary="Item condition (new, used, refurbished)" />
                        </ListItem>
                        <ListItem>
                            <ListItemText primary="location" secondary="Storage location (optional)" />
                        </ListItem>
                    </List>
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setOpen(false)}>Cancel</Button>
                    <Button onClick={handleImport} variant="contained">
                        Start Import
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

// Custom bulk delete with additional validation
const BulkDeleteAction = () => {
    const { selectedIds, data } = useListContext();
    const { permissions } = usePermissions();

    const canDelete = permissions?.role === 'admin';

    if (!canDelete) return null;

    const selectedContainers = selectedIds.map(id => data?.find((container: Container) => container.id === id)).filter(Boolean) as Container[];
    const containersWithItems = selectedContainers.filter(container => 
        container.inventoryItems && container.inventoryItems.length > 0
    );

    if (containersWithItems.length > 0) {
        return (
            <Button
                startIcon={<DeleteIcon />}
                disabled
                title="Cannot delete containers that contain inventory items"
            >
                Delete (Blocked)
            </Button>
        );
    }

    return <BulkDeleteButton />;
};

// Main bulk actions component
export const ContainerBulkActions = () => {
    const { permissions } = usePermissions();
    const { selectedIds } = useListContext();

    if (selectedIds.length === 0) return null;

    return (
        <Box sx={{ display: 'flex', gap: 1 }}>
            <BulkStatusUpdate />
            <BulkImportAction />
            <BulkDeleteAction />
        </Box>
    );
};

export default ContainerBulkActions;
