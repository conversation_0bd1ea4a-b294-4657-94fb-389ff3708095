import React from 'react';
import {
    List,
    Datagrid,
    TextField,
    DateField,
    NumberField,
    SelectField,
    SearchInput,
    DateInput,
    SelectInput,
    TopToolbar,
    CreateButton,
    ExportButton,
    BulkDeleteButton,
    BulkUpdateButton,
    usePermissions,
    useRecordContext,
    FunctionField,
    ChipField,
    FilterButton,
    useListContext,
} from 'react-admin';
import {
    Box,
    Chip,
    Typography,
    Card,
    CardContent,
    useTheme,
} from '@mui/material';
import {
    Inventory as InventoryIcon,
    LocalShipping as ShippingIcon,
    CheckCircle as CompletedIcon,
    Schedule as ProcessingIcon,
} from '@mui/icons-material';
import { Container } from '../types';

// Status choices for the select field
const statusChoices = [
    { id: 'arrived', name: 'Arrived' },
    { id: 'processing', name: 'Processing' },
    { id: 'completed', name: 'Completed' },
];

// Custom status field with color coding
const StatusField = () => {
    const record = useRecordContext<Container>();
    const theme = useTheme();
    
    if (!record) return null;

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'arrived':
                return theme.palette.info.main;
            case 'processing':
                return theme.palette.warning.main;
            case 'completed':
                return theme.palette.success.main;
            default:
                return theme.palette.grey[500];
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'arrived':
                return <ShippingIcon fontSize="small" />;
            case 'processing':
                return <ProcessingIcon fontSize="small" />;
            case 'completed':
                return <CompletedIcon fontSize="small" />;
            default:
                return <InventoryIcon fontSize="small" />;
        }
    };

    return (
        <Chip
            icon={getStatusIcon(record.status)}
            label={record.status.charAt(0).toUpperCase() + record.status.slice(1)}
            sx={{
                backgroundColor: getStatusColor(record.status),
                color: 'white',
                fontWeight: 'bold',
            }}
            size="small"
        />
    );
};

// Container filters
const containerFilters = [
    <SearchInput source="q" placeholder="Search containers..." alwaysOn />,
    <SelectInput
        source="status"
        choices={statusChoices}
        label="Status"
        emptyText="All statuses"
    />,
    <DateInput source="arrival_date_from" label="Arrival Date From" />,
    <DateInput source="arrival_date_to" label="Arrival Date To" />,
];

// Custom list actions with role-based permissions
const ContainerListActions = () => {
    const { permissions } = usePermissions();
    const canCreate = permissions?.role === 'admin' || permissions?.role === 'manager';

    return (
        <TopToolbar>
            <FilterButton />
            {canCreate && <CreateButton />}
            <ExportButton />
        </TopToolbar>
    );
};

// Bulk actions with role-based permissions
const ContainerBulkActionButtons = () => {
    const { permissions } = usePermissions();
    const canEdit = permissions?.role === 'admin' || permissions?.role === 'manager';

    if (!canEdit) return null;

    return (
        <>
            <BulkUpdateButton
                label="Update Status"
                data={{ status: 'processing' }}
                mutationMode="pessimistic"
            />
            <BulkDeleteButton />
        </>
    );
};

// Progress indicator for container processing
const ProgressField = () => {
    const record = useRecordContext<Container>();
    
    if (!record) return null;

    const getProgress = (status: string) => {
        switch (status) {
            case 'arrived':
                return 33;
            case 'processing':
                return 66;
            case 'completed':
                return 100;
            default:
                return 0;
        }
    };

    const progress = getProgress(record.status);

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box
                sx={{
                    width: 60,
                    height: 8,
                    backgroundColor: 'grey.300',
                    borderRadius: 4,
                    overflow: 'hidden',
                }}
            >
                <Box
                    sx={{
                        width: `${progress}%`,
                        height: '100%',
                        backgroundColor: progress === 100 ? 'success.main' : 'primary.main',
                        transition: 'width 0.3s ease',
                    }}
                />
            </Box>
            <Typography variant="caption" color="text.secondary">
                {progress}%
            </Typography>
        </Box>
    );
};

// Days since arrival calculation
const DaysField = () => {
    const record = useRecordContext<Container>();
    
    if (!record) return null;

    const arrivalDate = new Date(record.arrival_date);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - arrivalDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return (
        <Typography variant="body2" color="text.secondary">
            {diffDays} day{diffDays !== 1 ? 's' : ''}
        </Typography>
    );
};

// Main container list component
export const ContainerList = () => {
    return (
        <List
            filters={containerFilters}
            actions={<ContainerListActions />}
            sort={{ field: 'arrival_date', order: 'DESC' }}
            perPage={25}
            title="Container Management"
        >
            <Datagrid
                bulkActionButtons={<ContainerBulkActionButtons />}
                rowClick="show"
                sx={{
                    '& .RaDatagrid-headerCell': {
                        fontWeight: 'bold',
                    },
                }}
            >
                <TextField source="container_number" label="Container Number" />
                <DateField source="arrival_date" label="Arrival Date" />
                <StatusField />
                <ProgressField />
                <NumberField source="total_items" label="Total Items" />
                <DaysField />
                <DateField source="created_at" label="Created" showTime />
            </Datagrid>
        </List>
    );
};

export default ContainerList;
