import React from 'react';
import {
    Show,
    SimpleShowLayout,
    TextField,
    DateField,
    NumberField,
    useRecordContext,
    TopToolbar,
    EditButton,
    DeleteButton,
    usePermissions,
    ReferenceManyField,
    Datagrid,
    FunctionField,
    useGetOne,
} from 'react-admin';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Grid,
    Chip,
    Stepper,
    Step,
    StepLabel,
    Divider,
    Alert,
    LinearProgress,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
} from '@mui/material';
import {
    CheckCircle as CompletedIcon,
    Schedule as ProcessingIcon,
    LocalShipping as ArrivedIcon,
    Inventory as InventoryIcon,
    TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { Container, InventoryItem } from '../types';

// Status configuration
const statusConfig = {
    arrived: {
        label: 'Arrived',
        icon: <ArrivedIcon />,
        color: 'info' as const,
        step: 0,
    },
    processing: {
        label: 'Processing',
        icon: <ProcessingIcon />,
        color: 'warning' as const,
        step: 1,
    },
    completed: {
        label: 'Completed',
        icon: <CompletedIcon />,
        color: 'success' as const,
        step: 2,
    },
};

const steps = ['Arrived', 'Processing', 'Completed'];

// Status display component
const StatusDisplay = () => {
    const record = useRecordContext<Container>();
    
    if (!record) return null;

    const config = statusConfig[record.status];
    
    return (
        <Chip
            icon={config?.icon}
            label={config?.label || record.status}
            color={config?.color || 'default'}
            variant="filled"
            size="medium"
            sx={{ fontWeight: 'bold' }}
        />
    );
};

// Progress stepper component
const ProgressStepper = () => {
    const record = useRecordContext<Container>();
    
    if (!record) return null;

    const currentStep = statusConfig[record.status]?.step || 0;

    return (
        <Card sx={{ mb: 3 }}>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    Processing Progress
                </Typography>
                <Stepper activeStep={currentStep} alternativeLabel>
                    {steps.map((label, index) => (
                        <Step key={label} completed={index < currentStep}>
                            <StepLabel>{label}</StepLabel>
                        </Step>
                    ))}
                </Stepper>
                <Box sx={{ mt: 2 }}>
                    <LinearProgress 
                        variant="determinate" 
                        value={(currentStep + 1) * 33.33} 
                        sx={{ height: 8, borderRadius: 4 }}
                    />
                </Box>
            </CardContent>
        </Card>
    );
};

// Container statistics component
const ContainerStats = () => {
    const record = useRecordContext<Container>();
    
    if (!record) return null;

    const arrivalDate = new Date(record.arrival_date);
    const today = new Date();
    const daysInSystem = Math.ceil((today.getTime() - arrivalDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const inventoryItems = record.inventoryItems || [];
    const processedItems = inventoryItems.filter(item => item.status !== 'pending').length;
    const processingRate = record.total_items > 0 ? (processedItems / record.total_items) * 100 : 0;

    const stats = [
        {
            label: 'Days in System',
            value: daysInSystem,
            icon: <TrendingUpIcon />,
            color: daysInSystem > 30 ? 'error' : daysInSystem > 14 ? 'warning' : 'success',
        },
        {
            label: 'Total Items',
            value: record.total_items,
            icon: <InventoryIcon />,
            color: 'primary',
        },
        {
            label: 'Processed Items',
            value: processedItems,
            icon: <CompletedIcon />,
            color: 'success',
        },
        {
            label: 'Processing Rate',
            value: `${processingRate.toFixed(1)}%`,
            icon: <TrendingUpIcon />,
            color: processingRate > 80 ? 'success' : processingRate > 50 ? 'warning' : 'error',
        },
    ];

    return (
        <Grid container spacing={2} sx={{ mb: 3 }}>
            {stats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                    <Card>
                        <CardContent sx={{ textAlign: 'center' }}>
                            <Box sx={{ color: `${stat.color}.main`, mb: 1 }}>
                                {stat.icon}
                            </Box>
                            <Typography variant="h4" color={`${stat.color}.main`}>
                                {stat.value}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {stat.label}
                            </Typography>
                        </CardContent>
                    </Card>
                </Grid>
            ))}
        </Grid>
    );
};

// Inventory items table component
const InventoryItemsTable = () => {
    const record = useRecordContext<Container>();
    
    if (!record || !record.inventoryItems) return null;

    const items = record.inventoryItems;

    if (items.length === 0) {
        return (
            <Alert severity="info" sx={{ mt: 2 }}>
                No inventory items found for this container. Items can be added through the bulk import feature.
            </Alert>
        );
    }

    return (
        <Card sx={{ mt: 3 }}>
            <CardContent>
                <Typography variant="h6" gutterBottom>
                    Inventory Items ({items.length})
                </Typography>
                <TableContainer component={Paper} variant="outlined">
                    <Table size="small">
                        <TableHead>
                            <TableRow>
                                <TableCell>Serial Number</TableCell>
                                <TableCell>Product</TableCell>
                                <TableCell>Condition</TableCell>
                                <TableCell>Status</TableCell>
                                <TableCell>Location</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {items.slice(0, 10).map((item) => (
                                <TableRow key={item.id}>
                                    <TableCell>{item.serial_number}</TableCell>
                                    <TableCell>
                                        {item.product ? 
                                            `${item.product.brand} ${item.product.model}` : 
                                            'Unknown Product'
                                        }
                                    </TableCell>
                                    <TableCell>
                                        <Chip 
                                            label={item.condition} 
                                            size="small" 
                                            color={item.condition === 'new' ? 'success' : 'default'}
                                        />
                                    </TableCell>
                                    <TableCell>
                                        <Chip 
                                            label={item.status} 
                                            size="small" 
                                            color={item.status === 'available' ? 'success' : 'warning'}
                                        />
                                    </TableCell>
                                    <TableCell>{item.location || 'Not assigned'}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
                {items.length > 10 && (
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        Showing first 10 items. Total: {items.length} items.
                    </Typography>
                )}
            </CardContent>
        </Card>
    );
};

// Custom actions with role-based permissions
const ContainerShowActions = () => {
    const { permissions } = usePermissions();
    const canEdit = permissions?.role === 'admin' || permissions?.role === 'manager';
    const canDelete = permissions?.role === 'admin';

    return (
        <TopToolbar>
            {canEdit && <EditButton />}
            {canDelete && <DeleteButton />}
        </TopToolbar>
    );
};

// Main container show component
export const ContainerShow = () => {
    return (
        <Show actions={<ContainerShowActions />} title="Container Details">
            <SimpleShowLayout>
                <Box sx={{ width: '100%' }}>
                    <ProgressStepper />
                    <ContainerStats />
                    
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom color="primary">
                                Container Information
                            </Typography>
                            
                            <Grid container spacing={3}>
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Container Number
                                    </Typography>
                                    <TextField source="container_number" />
                                </Grid>
                                
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Status
                                    </Typography>
                                    <StatusDisplay />
                                </Grid>
                                
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Arrival Date
                                    </Typography>
                                    <DateField source="arrival_date" />
                                </Grid>
                                
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Total Items
                                    </Typography>
                                    <NumberField source="total_items" />
                                </Grid>
                                
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Created At
                                    </Typography>
                                    <DateField source="created_at" showTime />
                                </Grid>
                                
                                <Grid item xs={12} sm={6}>
                                    <Typography variant="subtitle2" color="text.secondary">
                                        Last Updated
                                    </Typography>
                                    <DateField source="updated_at" showTime />
                                </Grid>
                            </Grid>
                        </CardContent>
                    </Card>
                    
                    <InventoryItemsTable />
                </Box>
            </SimpleShowLayout>
        </Show>
    );
};

export default ContainerShow;
