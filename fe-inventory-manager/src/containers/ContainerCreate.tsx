import React from 'react';
import {
    Create,
    SimpleForm,
    TextInput,
    DateInput,
    SelectInput,
    NumberInput,
    required,
    minValue,
    maxLength,
    usePermissions,
    useNotify,
    useRedirect,
    SaveButton,
    Toolbar,
} from 'react-admin';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Alert,
    Grid,
} from '@mui/material';
import { ContainerFormData } from '../types';

// Status choices for container creation
const statusChoices = [
    { id: 'arrived', name: 'Arrived' },
    { id: 'processing', name: 'Processing' },
    { id: 'completed', name: 'Completed' },
];

// Custom validation for container number format
const validateContainerNumber = (value: string) => {
    if (!value) return 'Container number is required';
    
    // Check format: should be alphanumeric with optional hyphens
    const containerRegex = /^[A-Z0-9-]+$/i;
    if (!containerRegex.test(value)) {
        return 'Container number should contain only letters, numbers, and hyphens';
    }
    
    if (value.length < 3) {
        return 'Container number must be at least 3 characters long';
    }
    
    if (value.length > 50) {
        return 'Container number must be less than 50 characters';
    }
    
    return undefined;
};

// Custom validation for arrival date
const validateArrivalDate = (value: string) => {
    if (!value) return 'Arrival date is required';
    
    const arrivalDate = new Date(value);
    const today = new Date();
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(today.getFullYear() - 1);
    
    if (arrivalDate > today) {
        return 'Arrival date cannot be in the future';
    }
    
    if (arrivalDate < oneYearAgo) {
        return 'Arrival date cannot be more than one year ago';
    }
    
    return undefined;
};

// Custom toolbar with role-based permissions
const ContainerCreateToolbar = () => {
    const { permissions } = usePermissions();
    const canCreate = permissions?.role === 'admin' || permissions?.role === 'manager';

    if (!canCreate) {
        return (
            <Toolbar>
                <Alert severity="error" sx={{ width: '100%' }}>
                    You don't have permission to create containers. Only administrators and managers can create containers.
                </Alert>
            </Toolbar>
        );
    }

    return (
        <Toolbar>
            <SaveButton />
        </Toolbar>
    );
};

// Transform data before submission
const transform = (data: ContainerFormData) => {
    return {
        ...data,
        container_number: data.container_number.toUpperCase().trim(),
        total_items: data.total_items || 0,
        status: data.status || 'arrived',
    };
};

// Main container create component
export const ContainerCreate = () => {
    const { permissions } = usePermissions();
    const notify = useNotify();
    const redirect = useRedirect();

    const canCreate = permissions?.role === 'admin' || permissions?.role === 'manager';

    const onSuccess = () => {
        notify('Container created successfully', { type: 'success' });
        redirect('list', 'containers');
    };

    const onError = (error: any) => {
        console.error('Container creation error:', error);
        
        if (error?.body?.message?.includes('already exists')) {
            notify('Container number already exists. Please use a different number.', { type: 'error' });
        } else if (error?.body?.error === 'Validation error') {
            notify('Please check your input and try again.', { type: 'error' });
        } else {
            notify('Failed to create container. Please try again.', { type: 'error' });
        }
    };

    if (!canCreate) {
        return (
            <Box sx={{ p: 3 }}>
                <Card>
                    <CardContent>
                        <Alert severity="error">
                            <Typography variant="h6" gutterBottom>
                                Access Denied
                            </Typography>
                            <Typography>
                                You don't have permission to create containers. Only administrators and managers can create containers.
                            </Typography>
                        </Alert>
                    </CardContent>
                </Card>
            </Box>
        );
    }

    return (
        <Create
            title="Create New Container"
            transform={transform}
            mutationOptions={{
                onSuccess,
                onError,
            }}
        >
            <SimpleForm toolbar={<ContainerCreateToolbar />}>
                <Box sx={{ width: '100%', maxWidth: 600 }}>
                    <Typography variant="h6" gutterBottom color="primary">
                        Container Information
                    </Typography>
                    
                    <TextInput
                        source="container_number"
                        label="Container Number"
                        validate={[required(), validateContainerNumber]}
                        fullWidth
                        helperText="Enter a unique container identifier (e.g., CONT-2024-001)"
                        sx={{ mb: 2 }}
                    />

                    <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                        <DateInput
                            source="arrival_date"
                            label="Arrival Date"
                            validate={[required(), validateArrivalDate]}
                            helperText="Date when container arrived"
                            sx={{ flex: 1 }}
                        />

                        <SelectInput
                            source="status"
                            label="Initial Status"
                            choices={statusChoices}
                            defaultValue="arrived"
                            helperText="Current processing status"
                            sx={{ flex: 1 }}
                        />
                    </Box>

                    <NumberInput
                        source="total_items"
                        label="Total Items"
                        validate={[minValue(0)]}
                        defaultValue={0}
                        fullWidth
                        helperText="Expected number of items in container"
                        sx={{ mb: 2 }}
                    />

                    <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                            <strong>Note:</strong> After creating the container, you can add inventory items through the bulk import feature or individual item creation.
                        </Typography>
                    </Alert>
                </Box>
            </SimpleForm>
        </Create>
    );
};

export default ContainerCreate;
