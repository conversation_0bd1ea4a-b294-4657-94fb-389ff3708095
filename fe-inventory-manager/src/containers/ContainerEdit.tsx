import React, { useState } from 'react';
import {
    Edit,
    SimpleForm,
    TextInput,
    DateInput,
    SelectInput,
    NumberInput,
    required,
    minValue,
    usePermissions,
    useNotify,
    useRecordContext,
    SaveButton,
    Toolbar,
    useGetOne,
} from 'react-admin';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Alert,
    Grid,
    Chip,
    Stepper,
    Step,
    StepLabel,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
} from '@mui/material';
import {
    CheckCircle as CompletedIcon,
    Schedule as ProcessingIcon,
    LocalShipping as ArrivedIcon,
} from '@mui/icons-material';
import { Container } from '../types';

// Status workflow configuration
const statusWorkflow: Record<string, {
    label: string;
    icon: React.ReactElement;
    color: 'info' | 'warning' | 'success';
    nextStates: string[];
    step: number;
}> = {
    arrived: {
        label: 'Arrived',
        icon: <ArrivedIcon />,
        color: 'info' as const,
        nextStates: ['processing'],
        step: 0,
    },
    processing: {
        label: 'Processing',
        icon: <ProcessingIcon />,
        color: 'warning' as const,
        nextStates: ['completed'],
        step: 1,
    },
    completed: {
        label: 'Completed',
        icon: <CompletedIcon />,
        color: 'success' as const,
        nextStates: [],
        step: 2,
    },
};

const steps = ['Arrived', 'Processing', 'Completed'];

// Status workflow stepper component
const StatusWorkflowStepper = () => {
    const record = useRecordContext<Container>();
    
    if (!record) return null;

    const currentStep = statusWorkflow[record.status]?.step || 0;

    return (
        <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
                Container Status Workflow
            </Typography>
            <Stepper activeStep={currentStep} alternativeLabel>
                {steps.map((label, index) => (
                    <Step key={label} completed={index < currentStep}>
                        <StepLabel>{label}</StepLabel>
                    </Step>
                ))}
            </Stepper>
        </Box>
    );
};

// Status transition validation
const validateStatusTransition = (value: string, allValues: any) => {
    const currentStatus = allValues.status || 'arrived';
    const allowedTransitions = statusWorkflow[currentStatus]?.nextStates || [];
    
    if (value === currentStatus) {
        return undefined; // No change is always valid
    }
    
    if (!allowedTransitions.includes(value)) {
        const allowedText = allowedTransitions.length > 0 
            ? allowedTransitions.join(', ') 
            : 'none (final state)';
        return `Invalid status transition. From ${currentStatus}, you can only transition to: ${allowedText}`;
    }
    
    return undefined;
};

// Status confirmation dialog
const StatusConfirmationDialog = ({ 
    open, 
    onClose, 
    onConfirm, 
    currentStatus, 
    newStatus 
}: {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    currentStatus: string;
    newStatus: string;
}) => {
    return (
        <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
            <DialogTitle>Confirm Status Change</DialogTitle>
            <DialogContent>
                <Typography gutterBottom>
                    Are you sure you want to change the container status from{' '}
                    <strong>{statusWorkflow[currentStatus]?.label}</strong> to{' '}
                    <strong>{statusWorkflow[newStatus]?.label}</strong>?
                </Typography>
                <Alert severity="warning" sx={{ mt: 2 }}>
                    This action cannot be undone. Status changes are tracked for audit purposes.
                </Alert>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={onConfirm} variant="contained" color="primary">
                    Confirm Change
                </Button>
            </DialogActions>
        </Dialog>
    );
};

// Custom status input with workflow validation
const StatusWorkflowInput = () => {
    const record = useRecordContext<Container>();
    const [confirmDialog, setConfirmDialog] = useState(false);
    const [pendingStatus, setPendingStatus] = useState('');

    if (!record) return null;

    const currentStatus = record.status;
    const allowedStatuses = statusWorkflow[currentStatus]?.nextStates || [];
    
    // Create choices based on current status and allowed transitions
    const statusChoices = [
        { id: currentStatus, name: statusWorkflow[currentStatus]?.label || currentStatus },
        ...allowedStatuses.map(status => ({
            id: status,
            name: statusWorkflow[status]?.label || status,
        })),
    ];

    const handleStatusChange = (event: any) => {
        const value = typeof event === 'string' ? event : event.target?.value;
        if (value !== currentStatus) {
            setPendingStatus(value);
            setConfirmDialog(true);
        }
    };

    return (
        <>
            <SelectInput
                source="status"
                label="Status"
                choices={statusChoices}
                validate={[required(), validateStatusTransition]}
                fullWidth
                helperText={
                    allowedStatuses.length > 0
                        ? `Can transition to: ${allowedStatuses.join(', ')}`
                        : 'Final status - no further transitions allowed'
                }
                onChange={handleStatusChange}
            />
            <StatusConfirmationDialog
                open={confirmDialog}
                onClose={() => setConfirmDialog(false)}
                onConfirm={() => setConfirmDialog(false)}
                currentStatus={currentStatus}
                newStatus={pendingStatus}
            />
        </>
    );
};

// Custom toolbar with role-based permissions
const ContainerEditToolbar = () => {
    const { permissions } = usePermissions();
    const canEdit = permissions?.role === 'admin' || permissions?.role === 'manager';

    if (!canEdit) {
        return (
            <Toolbar>
                <Alert severity="error" sx={{ width: '100%' }}>
                    You don't have permission to edit containers. Only administrators and managers can edit containers.
                </Alert>
            </Toolbar>
        );
    }

    return (
        <Toolbar>
            <SaveButton />
        </Toolbar>
    );
};

// Transform data before submission
const transform = (data: any) => {
    return {
        ...data,
        container_number: data.container_number?.toUpperCase().trim(),
        total_items: data.total_items || 0,
    };
};

// Main container edit component
export const ContainerEdit = () => {
    const { permissions } = usePermissions();
    const notify = useNotify();

    const canEdit = permissions?.role === 'admin' || permissions?.role === 'manager';

    const onSuccess = () => {
        notify('Container updated successfully', { type: 'success' });
    };

    const onError = (error: any) => {
        console.error('Container update error:', error);
        
        if (error?.body?.message?.includes('Invalid status transition')) {
            notify('Invalid status transition. Please check the workflow rules.', { type: 'error' });
        } else if (error?.body?.message?.includes('already exists')) {
            notify('Container number already exists. Please use a different number.', { type: 'error' });
        } else {
            notify('Failed to update container. Please try again.', { type: 'error' });
        }
    };

    if (!canEdit) {
        return (
            <Box sx={{ p: 3 }}>
                <Card>
                    <CardContent>
                        <Alert severity="error">
                            <Typography variant="h6" gutterBottom>
                                Access Denied
                            </Typography>
                            <Typography>
                                You don't have permission to edit containers. Only administrators and managers can edit containers.
                            </Typography>
                        </Alert>
                    </CardContent>
                </Card>
            </Box>
        );
    }

    return (
        <Edit
            title="Edit Container"
            transform={transform}
            mutationOptions={{
                onSuccess,
                onError,
            }}
        >
            <SimpleForm toolbar={<ContainerEditToolbar />}>
                <Box sx={{ width: '100%', maxWidth: 800 }}>
                    <StatusWorkflowStepper />
                    
                    <Typography variant="h6" gutterBottom color="primary">
                        Container Information
                    </Typography>
                    
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                            <TextInput
                                source="container_number"
                                label="Container Number"
                                validate={[required()]}
                                fullWidth
                                sx={{ mb: 2 }}
                            />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <DateInput
                                source="arrival_date"
                                label="Arrival Date"
                                validate={[required()]}
                                fullWidth
                                sx={{ mb: 2 }}
                            />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <StatusWorkflowInput />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                            <NumberInput
                                source="total_items"
                                label="Total Items"
                                validate={[minValue(0)]}
                                fullWidth
                                sx={{ mb: 2 }}
                            />
                        </Grid>
                    </Grid>

                    <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                            <strong>Status Workflow:</strong> Containers follow a strict workflow: Arrived → Processing → Completed. 
                            Status changes are permanent and tracked for audit purposes.
                        </Typography>
                    </Alert>
                </Box>
            </SimpleForm>
        </Edit>
    );
};

export default ContainerEdit;
