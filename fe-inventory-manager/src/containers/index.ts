import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import ContainerList from './ContainerList';
import ContainerCreate from './ContainerCreate';
import ContainerEdit from './ContainerEdit';
import ContainerShow from './ContainerShow';
import ContainerFilters from './ContainerFilters';
import ContainerBulkActions from './ContainerBulkActions';
import { Container } from '../types';

// Container resource configuration for React-Admin
const containers = {
    list: ContainerList,
    create: ContainerCreate,
    edit: ContainerEdit,
    show: ContainerShow,
    icon: LocalShippingIcon,
    recordRepresentation: (record: Container) => `Container ${record.container_number}`,
};

export default containers;

export {
    ContainerList,
    ContainerCreate,
    ContainerEdit,
    ContainerShow,
    ContainerFilters,
    ContainerBulkActions,
};
