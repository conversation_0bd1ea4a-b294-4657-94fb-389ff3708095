import React, { useState } from 'react';
import {
    SearchInput,
    SelectInput,
    DateInput,
    NumberInput,
    useListContext,
} from 'react-admin';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Grid,
    Collapse,
    Button,
    Chip,
    IconButton,
    Divider,
} from '@mui/material';
import {
    ExpandMore as ExpandMoreIcon,
    ExpandLess as ExpandLessIcon,
    Clear as ClearIcon,
    FilterList as FilterListIcon,
} from '@mui/icons-material';

// Status choices for filtering
const statusChoices = [
    { id: 'arrived', name: 'Arrived' },
    { id: 'processing', name: 'Processing' },
    { id: 'completed', name: 'Completed' },
];

// Date range presets
const dateRangePresets = [
    { id: 'today', name: 'Today', days: 0 },
    { id: 'week', name: 'This Week', days: 7 },
    { id: 'month', name: 'This Month', days: 30 },
    { id: 'quarter', name: 'This Quarter', days: 90 },
    { id: 'year', name: 'This Year', days: 365 },
];

// Item count ranges
const itemCountRanges = [
    { id: 'small', name: 'Small (1-100)', min: 1, max: 100 },
    { id: 'medium', name: 'Medium (101-500)', min: 101, max: 500 },
    { id: 'large', name: 'Large (501-1000)', min: 501, max: 1000 },
    { id: 'xlarge', name: 'Extra Large (1000+)', min: 1001, max: null },
];

// Quick filter component
const QuickFilters = () => {
    const { filterValues, setFilters } = useListContext();

    const handleStatusFilter = (status: string) => {
        setFilters({ ...filterValues, status }, []);
    };

    const handleDateRangeFilter = (preset: any) => {
        const today = new Date();
        const fromDate = new Date();
        fromDate.setDate(today.getDate() - preset.days);

        setFilters({
            ...filterValues,
            arrival_date_from: fromDate.toISOString().split('T')[0],
            arrival_date_to: today.toISOString().split('T')[0],
        }, []);
    };

    const handleItemCountFilter = (range: any) => {
        setFilters({
            ...filterValues,
            total_items_min: range.min,
            total_items_max: range.max,
        }, []);
    };

    const clearFilters = () => {
        setFilters({}, []);
    };

    const activeFiltersCount = Object.keys(filterValues).filter(
        key => filterValues[key] !== undefined && filterValues[key] !== ''
    ).length;

    return (
        <Card sx={{ mb: 2 }}>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FilterListIcon />
                        Quick Filters
                    </Typography>
                    {activeFiltersCount > 0 && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                                label={`${activeFiltersCount} active filter${activeFiltersCount > 1 ? 's' : ''}`}
                                size="small"
                                color="primary"
                            />
                            <Button
                                size="small"
                                onClick={clearFilters}
                                startIcon={<ClearIcon />}
                            >
                                Clear All
                            </Button>
                        </Box>
                    )}
                </Box>

                <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2" gutterBottom>
                            Status
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                            {statusChoices.map((status) => (
                                <Chip
                                    key={status.id}
                                    label={status.name}
                                    onClick={() => handleStatusFilter(status.id)}
                                    color={filterValues.status === status.id ? 'primary' : 'default'}
                                    variant={filterValues.status === status.id ? 'filled' : 'outlined'}
                                    size="small"
                                    clickable
                                />
                            ))}
                        </Box>
                    </Grid>

                    <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2" gutterBottom>
                            Arrival Date
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                            {dateRangePresets.map((preset) => (
                                <Chip
                                    key={preset.id}
                                    label={preset.name}
                                    onClick={() => handleDateRangeFilter(preset)}
                                    color="default"
                                    variant="outlined"
                                    size="small"
                                    clickable
                                />
                            ))}
                        </Box>
                    </Grid>

                    <Grid item xs={12} md={4}>
                        <Typography variant="subtitle2" gutterBottom>
                            Container Size
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                            {itemCountRanges.map((range) => (
                                <Chip
                                    key={range.id}
                                    label={range.name}
                                    onClick={() => handleItemCountFilter(range)}
                                    color="default"
                                    variant="outlined"
                                    size="small"
                                    clickable
                                />
                            ))}
                        </Box>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );
};

// Advanced filters component
const AdvancedFilters = () => {
    const [expanded, setExpanded] = useState(false);

    return (
        <Card sx={{ mb: 2 }}>
            <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Typography variant="h6">
                        Advanced Filters
                    </Typography>
                    <IconButton onClick={() => setExpanded(!expanded)}>
                        {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                    </IconButton>
                </Box>

                <Collapse in={expanded}>
                    <Divider sx={{ my: 2 }} />
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <SearchInput
                                source="q"
                                placeholder="Search containers..."
                                alwaysOn
                                fullWidth
                            />
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <SelectInput
                                source="status"
                                choices={statusChoices}
                                label="Status"
                                emptyText="All statuses"
                                fullWidth
                            />
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <DateInput
                                source="arrival_date_from"
                                label="Arrival Date From"
                                fullWidth
                            />
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <DateInput
                                source="arrival_date_to"
                                label="Arrival Date To"
                                fullWidth
                            />
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <NumberInput
                                source="total_items_min"
                                label="Minimum Items"
                                fullWidth
                            />
                        </Grid>

                        <Grid item xs={12} md={6}>
                            <NumberInput
                                source="total_items_max"
                                label="Maximum Items"
                                fullWidth
                            />
                        </Grid>
                    </Grid>
                </Collapse>
            </CardContent>
        </Card>
    );
};

// Active filters display
const ActiveFiltersDisplay = () => {
    const { filterValues, setFilters } = useListContext();

    const removeFilter = (key: string) => {
        const newFilters = { ...filterValues };
        delete newFilters[key];
        setFilters(newFilters, []);
    };

    const activeFilters = Object.entries(filterValues).filter(
        ([key, value]) => value !== undefined && value !== ''
    );

    if (activeFilters.length === 0) return null;

    const getFilterLabel = (key: string, value: any) => {
        switch (key) {
            case 'status':
                return `Status: ${statusChoices.find(s => s.id === value)?.name || value}`;
            case 'arrival_date_from':
                return `From: ${value}`;
            case 'arrival_date_to':
                return `To: ${value}`;
            case 'total_items_min':
                return `Min Items: ${value}`;
            case 'total_items_max':
                return `Max Items: ${value}`;
            case 'q':
                return `Search: "${value}"`;
            default:
                return `${key}: ${value}`;
        }
    };

    return (
        <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
                Active Filters:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {activeFilters.map(([key, value]) => (
                    <Chip
                        key={key}
                        label={getFilterLabel(key, value)}
                        onDelete={() => removeFilter(key)}
                        color="primary"
                        variant="outlined"
                        size="small"
                    />
                ))}
            </Box>
        </Box>
    );
};

// Main container filters component
export const ContainerFilters = () => {
    return (
        <Box>
            <QuickFilters />
            <AdvancedFilters />
            <ActiveFiltersDisplay />
        </Box>
    );
};

export default ContainerFilters;
