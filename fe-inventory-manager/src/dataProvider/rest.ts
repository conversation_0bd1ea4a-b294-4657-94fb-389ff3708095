import simpleRestProvider from 'ra-data-simple-rest';
import { DataProvider, fetchUtils } from 'react-admin';

const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000';

// Custom httpClient to handle JWT authentication
const httpClient = (url: string, options: fetchUtils.Options = {}) => {
    if (!options.headers) {
        options.headers = new Headers({ Accept: 'application/json' });
    }

    // Add JWT token to requests
    const token = localStorage.getItem('token');
    if (token) {
        (options.headers as Headers).set('Authorization', `Bearer ${token}`);
    }

    return fetchUtils.fetchJson(url, options);
};

// Create the base data provider
const baseDataProvider = simpleRestProvider(`${apiUrl}/api`, httpClient);

// Custom data provider to handle backend API structure
const dataProvider: DataProvider = {
    ...baseDataProvider,

    // Override getList to handle backend pagination format
    getList: (resource, params) => {
        // Handle mock categories since backend doesn't have them
        if (resource === 'categories') {
            return Promise.resolve({
                data: [
                    { id: 1, name: 'Laptops' },
                    { id: 2, name: 'Electronics' },
                ],
                total: 2,
            });
        }

        // Handle users resource with role-based filtering
        if (resource === 'users') {
            const { page, perPage } = params.pagination || { page: 1, perPage: 10 };
            const { field, order } = params.sort || { field: 'created_at', order: 'DESC' };

            // Get current user info for permission checking
            const currentUser = localStorage.getItem('user');
            const currentUserData = currentUser ? JSON.parse(currentUser) : null;

            const query = {
                page: page,
                limit: perPage,
                sort_by: field || 'created_at',
                sort_order: order?.toUpperCase() || 'DESC',
                ...params.filter,
            };

            // Add role-based filtering
            if (currentUserData?.role === 'manager') {
                // Managers should not see admin users
                query.role_not = 'admin';
            } else if (currentUserData?.role === 'employee') {
                // Employees should only see themselves
                query.user_id = currentUserData.id;
            }

            const url = `${apiUrl}/api/${resource}?${new URLSearchParams(query).toString()}`;

            return httpClient(url).then(({ json }) => {
                console.log('Users API Response:', json);

                let data = json.users || [];

                // Additional client-side filtering for security
                if (currentUserData?.role === 'manager') {
                    data = data.filter((user: any) => user.role !== 'admin');
                } else if (currentUserData?.role === 'employee') {
                    data = data.filter((user: any) => user.id === currentUserData.id);
                }

                return {
                    data: data,
                    total: json.pagination?.total || data.length,
                };
            });
        }

        const { page, perPage } = params.pagination || { page: 1, perPage: 10 };
        const { field, order } = params.sort || { field: 'created_at', order: 'DESC' };

        // Map frontend field names to backend field names
        const fieldMapping: Record<string, Record<string, string>> = {
            products: {
                'reference': 'brand',
                'sales': 'id',
                'stock': 'id',
                'id': 'id'
            },
            orders: {
                'id': 'order_date',
                'date': 'order_date',
                'total': 'total_amount',
                'status': 'status',
                'customer': 'customer_id'
            },
            customers: {
                'id': 'id',
                'name': 'company_name',
                'email': 'email'
            },
            users: {
                'id': 'id',
                'username': 'username',
                'email': 'email',
                'role': 'role',
                'is_active': 'is_active',
                'created_at': 'created_at',
                'last_login': 'last_login'
            },
            containers: {
                'id': 'id',
                'container_number': 'container_number',
                'arrival_date': 'arrival_date',
                'status': 'status',
                'total_items': 'total_items',
                'created_at': 'created_at',
                'updated_at': 'updated_at'
            }
        };

        const backendField = fieldMapping[resource]?.[field] || field;

        const query = {
            page: page,
            limit: perPage,
            sort_by: backendField,
            sort_order: order.toUpperCase(), // Backend expects uppercase
            ...params.filter,
        };

        const url = `${apiUrl}/api/${resource}?${new URLSearchParams(query).toString()}`;

        return httpClient(url).then(({ json }) => {
            console.log('API Response:', json); // Debug log

            let data = json.data || json[resource] || json.products || json.customers || json.orders || json.inventory || json.containers || [];

            // Transform backend data to match frontend expectations
            if (resource === 'products' && json.products) {
                data = json.products.map((product: any) => ({
                    id: product.id,
                    category_id: 1, // Default category since backend doesn't have categories
                    reference: `${product.brand} ${product.model}`,
                    width: 20, // Default dimensions
                    height: 30,
                    price: parseFloat(product.base_price || '0'),
                    thumbnail: product.image_url || 'https://via.placeholder.com/150',
                    image: product.image_url || 'https://via.placeholder.com/300',
                    description: JSON.stringify(product.specifications_json || {}),
                    stock: product.inventory_summary?.available_items || 0,
                    sales: product.inventory_summary?.sold_items || 0,
                    // Keep original data for reference
                    _original: product
                }));
            } else if (resource === 'orders' && json.orders) {
                data = json.orders.map((order: any) => ({
                    id: order.id,
                    date: order.order_date,
                    reference: order.order_number || `ORD-${order.id}`,
                    customer_id: order.customer_id,
                    total: parseFloat(order.total_amount || '0'),
                    status: order.status,
                    basket: order.items || [],
                    _original: order
                }));
            } else if (resource === 'customers' && json.customers) {
                data = json.customers.map((customer: any) => ({
                    id: customer.id,
                    first_name: customer.contact_person?.split(' ')[0] || '',
                    last_name: customer.contact_person?.split(' ').slice(1).join(' ') || '',
                    email: customer.email,
                    address: customer.address,
                    city: customer.address?.split(',')[1]?.trim() || '',
                    stateAbbr: customer.address?.split(',')[2]?.trim() || '',
                    zipcode: customer.address?.split(',')[3]?.trim() || '',
                    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(customer.contact_person || 'Customer')}&background=random`,
                    birthday: customer.created_at,
                    first_seen: customer.created_at,
                    last_seen: customer.updated_at,
                    has_ordered: true,
                    latest_purchase: customer.updated_at,
                    has_newsletter: true,
                    groups: [customer.pricing_tier || 'regular'],
                    nb_commands: 0,
                    total_spent: 0,
                    _original: customer
                }));
            } else if (resource === 'containers' && json.containers) {
                data = json.containers.map((container: any) => ({
                    id: container.id,
                    container_number: container.container_number,
                    arrival_date: container.arrival_date,
                    status: container.status,
                    total_items: container.total_items,
                    created_at: container.created_at,
                    updated_at: container.updated_at,
                    created_by: container.created_by,
                    updated_by: container.updated_by,
                    inventoryItems: container.inventoryItems || [],
                    _original: container
                }));
            }

            return {
                data: data,
                total: json.total || json.pagination?.total_items || json.pagination?.total || 0,
            };
        });
    },

    // Override getOne to handle backend response format
    getOne: (resource, params) =>
        httpClient(`${apiUrl}/api/${resource}/${params.id}`).then(({ json }) => {
            let data = json.data || json[resource.slice(0, -1)] || json;

            // Transform backend data to match frontend expectations
            if (resource === 'products' && data) {
                data = {
                    id: data.id,
                    category_id: 1,
                    reference: `${data.brand} ${data.model}`,
                    width: 20,
                    height: 30,
                    price: parseFloat(data.base_price || '0'),
                    thumbnail: data.image_url || 'https://via.placeholder.com/150',
                    image: data.image_url || 'https://via.placeholder.com/300',
                    description: JSON.stringify(data.specifications_json || {}),
                    stock: data.inventory_summary?.available_items || 0,
                    sales: data.inventory_summary?.sold_items || 0,
                    _original: data
                };
            } else if (resource === 'users' && data) {
                // Users data is already in the correct format from backend
                data = json.user || data;
            } else if (resource === 'containers' && data) {
                // Transform container data to match frontend expectations
                data = {
                    id: data.id,
                    container_number: data.container_number,
                    arrival_date: data.arrival_date,
                    status: data.status,
                    total_items: data.total_items,
                    created_at: data.created_at,
                    updated_at: data.updated_at,
                    created_by: data.created_by,
                    updated_by: data.updated_by,
                    inventoryItems: data.inventoryItems || [],
                    _original: data
                };
            }

            return { data };
        }),

    // Override create to handle backend response format
    create: (resource, params) => {
        // Handle user creation through auth/register endpoint with permission check
        if (resource === 'users') {
            const currentUser = localStorage.getItem('user');
            const currentUserData = currentUser ? JSON.parse(currentUser) : null;

            // Only admins can create users
            if (currentUserData?.role !== 'admin') {
                return Promise.reject(new Error('Access denied. Only administrators can create users.'));
            }

            return httpClient(`${apiUrl}/api/auth/register`, {
                method: 'POST',
                body: JSON.stringify(params.data),
            }).then(({ json }) => ({
                data: json.user || { ...params.data, id: json.data?.id || json.id },
            }));
        }

        return httpClient(`${apiUrl}/api/${resource}`, {
            method: 'POST',
            body: JSON.stringify(params.data),
        }).then(({ json }) => ({
            data: { ...params.data, id: json.data?.id || json.id },
        }));
    },

    // Override update to handle backend response format with permission checks
    update: (resource, params) => {
        if (resource === 'users') {
            const currentUser = localStorage.getItem('user');
            const currentUserData = currentUser ? JSON.parse(currentUser) : null;

            if (!currentUserData) {
                return Promise.reject(new Error('Authentication required.'));
            }

            const isOwnProfile = params.id === currentUserData.id;
            const isAdmin = currentUserData.role === 'admin';
            const isManager = currentUserData.role === 'manager';

            // Check if user can edit this record
            if (!isAdmin && !isManager && !isOwnProfile) {
                return Promise.reject(new Error('Access denied. You can only edit your own profile.'));
            }

            // Managers cannot edit admin users
            if (isManager && params.previousData?.role === 'admin') {
                return Promise.reject(new Error('Access denied. Managers cannot edit administrator accounts.'));
            }

            // Only admins can change roles and status
            if (!isAdmin && (params.data.role !== undefined || params.data.is_active !== undefined)) {
                return Promise.reject(new Error('Access denied. Only administrators can change user roles or status.'));
            }
        }

        return httpClient(`${apiUrl}/api/${resource}/${params.id}`, {
            method: 'PUT',
            body: JSON.stringify(params.data),
        }).then(({ json }) => ({
            data: json.data || json,
        }));
    },

    // Override delete to handle backend response format with permission checks
    delete: (resource, params) => {
        if (resource === 'users') {
            const currentUser = localStorage.getItem('user');
            const currentUserData = currentUser ? JSON.parse(currentUser) : null;

            // Only admins can delete users
            if (currentUserData?.role !== 'admin') {
                return Promise.reject(new Error('Access denied. Only administrators can delete users.'));
            }

            // Prevent self-deletion
            if (params.id === currentUserData.id) {
                return Promise.reject(new Error('You cannot delete your own account.'));
            }
        }

        return httpClient(`${apiUrl}/api/${resource}/${params.id}`, {
            method: 'DELETE',
        }).then(({ json }) => ({
            data: json.data || { id: params.id },
        }));
    },
};

export default dataProvider;
