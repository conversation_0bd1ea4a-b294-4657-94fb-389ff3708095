import { DataProvider } from 'react-admin';

export default (type: string) => {
    // Create a proxy that handles async loading of the actual data provider
    const dataProviderPromise = getDataProvider(type);

    // Return a proxy that waits for the data provider to load
    const dataProviderProxy = new Proxy(defaultDataProvider, {
        get(_, name) {
            if (name === 'supportAbortSignal') {
                return true;
            }
            return (resource: string, params: any) => {
                return dataProviderPromise.then(dataProvider => {
                    return dataProvider[name.toString()](resource, params);
                });
            };
        },
    });

    return dataProviderProxy;
};

const getDataProvider = async (type: string): Promise<DataProvider> => {
    if (type === 'graphql') {
        return import('./graphql').then(factory => factory.default());
    }
    return import('./rest').then(provider => provider.default);
};

// Default data provider for proxy initialization
const defaultDataProvider: DataProvider = {
    // @ts-ignore
    create: () => Promise.resolve({ data: { id: 0 } }),
    // @ts-ignore
    delete: () => Promise.resolve({ data: {} }),
    deleteMany: () => Promise.resolve({}),
    getList: () => Promise.resolve({ data: [], total: 0 }),
    getMany: () => Promise.resolve({ data: [] }),
    getManyReference: () => Promise.resolve({ data: [], total: 0 }),
    // @ts-ignore
    getOne: () => Promise.resolve({ data: {} }),
    // @ts-ignore
    update: () => Promise.resolve({ data: {} }),
    updateMany: () => Promise.resolve({}),
};
