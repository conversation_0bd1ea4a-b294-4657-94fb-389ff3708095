import { AuthProvider } from 'react-admin';

const apiUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3000';

const authProvider: AuthProvider = {
    login: async ({ username, password }) => {
        try {
            const response = await fetch(`${apiUrl}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || 'Login failed');
            }

            const data = await response.json();

            // Store token and user info
            localStorage.setItem('token', data.token);
            localStorage.setItem('user', JSON.stringify(data.user));

            return Promise.resolve();
        } catch (error) {
            return Promise.reject(error);
        }
    },

    logout: async () => {
        try {
            const token = localStorage.getItem('token');
            if (token) {
                // Call backend logout endpoint
                await fetch(`${apiUrl}/api/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
            }
        } catch (error) {
            console.warn('Logout request failed:', error);
        } finally {
            // Always clear local storage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
        }
        return Promise.resolve();
    },

    checkError: (error) => {
        const status = error.status;
        if (status === 401 || status === 403) {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            return Promise.reject();
        }
        return Promise.resolve();
    },

    checkAuth: () => {
        const token = localStorage.getItem('token');
        return token ? Promise.resolve() : Promise.reject();
    },

    getPermissions: () => {
        const user = localStorage.getItem('user');
        if (user) {
            const userData = JSON.parse(user);
            return Promise.resolve(userData.role);
        }
        return Promise.resolve('');
    },

    getIdentity: () => {
        const user = localStorage.getItem('user');
        if (user) {
            const userData = JSON.parse(user);
            return Promise.resolve({
                id: userData.id,
                fullName: userData.username,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.username)}&background=random`,
            });
        }
        return Promise.reject();
    },
};

export default authProvider;
