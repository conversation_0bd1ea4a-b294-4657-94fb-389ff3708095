import polyglotI18nProvider from 'ra-i18n-polyglot';
import {
    Admin,
    CustomRoutes,
    Resource,
    localStorageStore,
    useStore,
    StoreContextProvider,
} from 'react-admin';
import { Route } from 'react-router';

import authProvider from './authProvider';
import categories from './categories';
import { Dashboard } from './dashboard';
import dataProvider from './dataProvider/rest';
import englishMessages from './i18n/en';
import invoices from './invoices';
import { Layout, Login } from './layout';
import orders from './orders';
import products from './products';
import reviews from './reviews';
import Segments from './segments/Segments';
import visitors from './visitors';
import users, { UserProfile, PasswordChange } from './users';
import containers from './containers';
import { themes, ThemeName } from './themes/themes';
import RBACTestPanel from './components/RBACTestPanel';

const i18nProvider = polyglotI18nProvider(
    locale => {
        if (locale === 'fr') {
            return import('./i18n/fr').then(messages => messages.default);
        }

        // Always fallback on english
        return englishMessages;
    },
    'en',
    [
        { locale: 'en', name: 'English' },
        { locale: 'fr', name: 'Français' },
    ]
);

const store = localStorageStore(undefined, 'ECommerce');

const App = () => {
    const [themeName] = useStore<ThemeName>('themeName', 'soft');
    const singleTheme = themes.find(theme => theme.name === themeName)?.single;
    const lightTheme = themes.find(theme => theme.name === themeName)?.light;
    const darkTheme = themes.find(theme => theme.name === themeName)?.dark;
    return (
        <Admin
            title="Inventory Management System"
            dataProvider={dataProvider}
            store={store}
            authProvider={authProvider}
            dashboard={Dashboard}
            loginPage={Login}
            layout={Layout}
            i18nProvider={i18nProvider}
            disableTelemetry
            theme={singleTheme}
            lightTheme={lightTheme}
            darkTheme={darkTheme}
            defaultTheme="light"
            requireAuth
        >
            <CustomRoutes>
                <Route path="/segments" element={<Segments />} />
                <Route path="/profile" element={<UserProfile />} />
                <Route path="/users/:id/password" element={<PasswordChange />} />
                <Route path="/rbac-test" element={<RBACTestPanel />} />
            </CustomRoutes>
            <Resource name="users" {...users} />
            <Resource name="customers" {...visitors} />
            <Resource name="orders" {...orders} />
            <Resource name="products" {...products} />
            <Resource name="inventory" list={products.list} />
            <Resource name="containers" {...containers} />
        </Admin>
    );
};

const AppWrapper = () => (
    <StoreContextProvider value={store}>
        <App />
    </StoreContextProvider>
);

export default AppWrapper;
