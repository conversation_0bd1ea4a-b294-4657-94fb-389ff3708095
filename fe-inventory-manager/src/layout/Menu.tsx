import * as React from 'react';
import { useState } from 'react';
import { Box } from '@mui/material';
import LabelIcon from '@mui/icons-material/Label';
import PeopleIcon from '@mui/icons-material/People';
import PersonIcon from '@mui/icons-material/Person';
import SecurityIcon from '@mui/icons-material/Security';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import {
    useTranslate,
    DashboardMenuItem,
    MenuItemLink,
    MenuProps,
    useSidebarState,
} from 'react-admin';
import clsx from 'clsx';
import { useUserPermissions } from '../hooks/useUserPermissions';

import visitors from '../visitors';
import orders from '../orders';
import invoices from '../invoices';
import products from '../products';
import categories from '../categories';
import reviews from '../reviews';
import containers from '../containers';
import SubMenu from './SubMenu';

type MenuName = 'menuCatalog' | 'menuSales' | 'menuCustomers' | 'menuInventory' | 'menuAdmin';

const Menu = ({ dense = false }: MenuProps) => {
    const [state, setState] = useState({
        menuCatalog: true,
        menuSales: true,
        menuCustomers: true,
        menuInventory: true,
        menuAdmin: true,
    });
    const translate = useTranslate();
    const [open] = useSidebarState();
    const { canAccessUserManagement } = useUserPermissions();

    const handleToggle = (menu: MenuName) => {
        setState(state => ({ ...state, [menu]: !state[menu] }));
    };

    return (
        <Box
            sx={{
                width: open ? 200 : 50,
                marginTop: 1,
                marginBottom: 1,
                transition: theme =>
                    theme.transitions.create('width', {
                        easing: theme.transitions.easing.sharp,
                        duration: theme.transitions.duration.leavingScreen,
                    }),
            }}
            className={clsx({
                'RaMenu-open': open,
                'RaMenu-closed': !open,
            })}
        >
            <DashboardMenuItem />
            <SubMenu
                handleToggle={() => handleToggle('menuSales')}
                isOpen={state.menuSales}
                name="pos.menu.sales"
                icon={<orders.icon />}
                dense={dense}
            >
                <MenuItemLink
                    to="/orders"
                    state={{ _scrollToTop: true }}
                    primaryText={translate(`resources.orders.name`, {
                        smart_count: 2,
                    })}
                    leftIcon={<orders.icon />}
                    dense={dense}
                />
                <MenuItemLink
                    to="/invoices"
                    state={{ _scrollToTop: true }}
                    primaryText={translate(`resources.invoices.name`, {
                        smart_count: 2,
                    })}
                    leftIcon={<invoices.icon />}
                    dense={dense}
                />
            </SubMenu>
            <SubMenu
                handleToggle={() => handleToggle('menuCatalog')}
                isOpen={state.menuCatalog}
                name="pos.menu.catalog"
                icon={<products.icon />}
                dense={dense}
            >
                <MenuItemLink
                    to="/products"
                    state={{ _scrollToTop: true }}
                    primaryText={translate(`resources.products.name`, {
                        smart_count: 2,
                    })}
                    leftIcon={<products.icon />}
                    dense={dense}
                />
                <MenuItemLink
                    to="/categories"
                    state={{ _scrollToTop: true }}
                    primaryText={translate(`resources.categories.name`, {
                        smart_count: 2,
                    })}
                    leftIcon={<categories.icon />}
                    dense={dense}
                />
            </SubMenu>
            <SubMenu
                handleToggle={() => handleToggle('menuInventory')}
                isOpen={state.menuInventory}
                name="Inventory Management"
                icon={<LocalShippingIcon />}
                dense={dense}
            >
                <MenuItemLink
                    to="/containers"
                    state={{ _scrollToTop: true }}
                    primaryText="Containers"
                    leftIcon={<LocalShippingIcon />}
                    dense={dense}
                />
                <MenuItemLink
                    to="/inventory"
                    state={{ _scrollToTop: true }}
                    primaryText="Inventory Items"
                    leftIcon={<products.icon />}
                    dense={dense}
                />
            </SubMenu>
            <SubMenu
                handleToggle={() => handleToggle('menuCustomers')}
                isOpen={state.menuCustomers}
                name="pos.menu.customers"
                icon={<visitors.icon />}
                dense={dense}
            >
                <MenuItemLink
                    to="/customers"
                    state={{ _scrollToTop: true }}
                    primaryText={translate(`resources.customers.name`, {
                        smart_count: 2,
                    })}
                    leftIcon={<visitors.icon />}
                    dense={dense}
                />
                <MenuItemLink
                    to="/segments"
                    state={{ _scrollToTop: true }}
                    primaryText={translate(`resources.segments.name`, {
                        smart_count: 2,
                    })}
                    leftIcon={<LabelIcon />}
                    dense={dense}
                />
            </SubMenu>
            <MenuItemLink
                to="/reviews"
                state={{ _scrollToTop: true }}
                primaryText={translate(`resources.reviews.name`, {
                    smart_count: 2,
                })}
                leftIcon={<reviews.icon />}
                dense={dense}
            />

            {/* Admin Section - Only visible to admin and manager */}
            {canAccessUserManagement() && (
                <SubMenu
                    handleToggle={() => handleToggle('menuAdmin')}
                    isOpen={state.menuAdmin}
                    name="Administration"
                    icon={<PeopleIcon />}
                    dense={dense}
                >
                    <MenuItemLink
                        to="/users"
                        state={{ _scrollToTop: true }}
                        primaryText="Users"
                        leftIcon={<PeopleIcon />}
                        dense={dense}
                    />
                    {process.env.NODE_ENV === 'development' && (
                        <MenuItemLink
                            to="/rbac-test"
                            state={{ _scrollToTop: true }}
                            primaryText="RBAC Test"
                            leftIcon={<SecurityIcon />}
                            dense={dense}
                        />
                    )}
                </SubMenu>
            )}

            {/* Profile Link - Always visible */}
            <MenuItemLink
                to="/profile"
                state={{ _scrollToTop: true }}
                primaryText="My Profile"
                leftIcon={<PersonIcon />}
                dense={dense}
            />
        </Box>
    );
};

export default Menu;
