import * as DataGenerator from 'data-generator-retail';

export type ThemeName = 'light' | 'dark';

export type Category = DataGenerator.Category;
export type Product = DataGenerator.Product;
export type Customer = DataGenerator.Customer;
export type Order = DataGenerator.Order;
export type Invoice = DataGenerator.Invoice;
export type Review = DataGenerator.Review;
export type BasketItem = DataGenerator.BasketItem;

// User types for the inventory management system
export interface User {
    id: string;
    username: string;
    email: string;
    role: 'admin' | 'manager' | 'employee';
    is_active: boolean;
    last_login?: string;
    created_at: string;
    updated_at: string;
}

export interface UserFormData {
    username: string;
    email: string;
    password?: string;
    confirmPassword?: string;
    role: 'admin' | 'manager' | 'employee';
    is_active?: boolean;
}

export interface PasswordChangeData {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}

// Container types for the inventory management system
export interface Container {
    id: number;
    container_number: string;
    arrival_date: string;
    status: 'arrived' | 'processing' | 'completed';
    total_items: number;
    created_at: string;
    updated_at: string;
    created_by?: string;
    updated_by?: string;
    deleted_at?: string;
    inventoryItems?: InventoryItem[];
}

export interface InventoryItem {
    id: number;
    serial_number: string;
    condition: string;
    status: string;
    location?: string;
    container_id: number;
    product_id: number;
    product?: {
        id: number;
        brand: string;
        model: string;
        specifications_json: any;
        base_price: string;
    };
    created_at: string;
    updated_at: string;
}

export interface ContainerFormData {
    container_number: string;
    arrival_date: string;
    status?: 'arrived' | 'processing' | 'completed';
    total_items?: number;
}

export interface ContainerFilters {
    container_number?: string;
    status?: string;
    arrival_date_from?: string;
    arrival_date_to?: string;
    q?: string;
}

declare global {
    interface Window {
        restServer: any;
    }
}
