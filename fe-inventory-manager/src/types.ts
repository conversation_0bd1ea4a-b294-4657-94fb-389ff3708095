import * as DataGenerator from 'data-generator-retail';

export type ThemeName = 'light' | 'dark';

export type Category = DataGenerator.Category;
export type Product = DataGenerator.Product;
export type Customer = DataGenerator.Customer;
export type Order = DataGenerator.Order;
export type Invoice = DataGenerator.Invoice;
export type Review = DataGenerator.Review;
export type BasketItem = DataGenerator.BasketItem;

// User types for the inventory management system
export interface User {
    id: string;
    username: string;
    email: string;
    role: 'admin' | 'manager' | 'employee';
    is_active: boolean;
    last_login?: string;
    created_at: string;
    updated_at: string;
}

export interface UserFormData {
    username: string;
    email: string;
    password?: string;
    confirmPassword?: string;
    role: 'admin' | 'manager' | 'employee';
    is_active?: boolean;
}

export interface PasswordChangeData {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
}

declare global {
    interface Window {
        restServer: any;
    }
}
