import { useMemo } from 'react';
import { usePermissions, useGetIdentity } from 'react-admin';
import { UserPermissions, UserRole } from '../utils/permissions';
import { User } from '../types';

/**
 * Custom hook for user permission management
 * Combines React-Admin's usePermissions and useGetIdentity with our permission system
 */
export const useUserPermissions = () => {
    const { permissions, isPending: isPermissionsPending } = usePermissions();
    const { identity, isLoading: isIdentityLoading } = useGetIdentity();

    const permissionChecker = useMemo(() => {
        if (!permissions || !identity?.id) {
            return null;
        }
        
        return new UserPermissions(permissions as UserRole, identity.id);
    }, [permissions, identity?.id]);

    const isLoading = isPermissionsPending || isIdentityLoading;

    return {
        permissions: permissions as UserRole,
        userId: identity?.id,
        permissionChecker,
        isLoading,
        
        // Convenience methods
        canViewUserList: () => permissionChecker?.canViewUserList() ?? false,
        canCreateUser: () => permissionChecker?.canCreateUser() ?? false,
        canViewUser: (user: User) => permissionChecker?.canViewUser(user) ?? false,
        canEditUser: (user: User) => permissionChecker?.canEditUser(user) ?? false,
        canDeleteUser: (user: User) => permissionChecker?.canDeleteUser(user) ?? false,
        canChangeUserRole: (user: User) => permissionChecker?.canChangeUserRole(user) ?? false,
        canChangeUserStatus: (user: User) => permissionChecker?.canChangeUserStatus(user) ?? false,
        canChangeUserPassword: (user: User) => permissionChecker?.canChangeUserPassword(user) ?? false,
        canAccessUserManagement: () => permissionChecker?.canAccessUserManagement() ?? false,
        getAllowedRolesToAssign: () => permissionChecker?.getAllowedRolesToAssign() ?? [],
        filterAllowedUsers: (users: User[]) => permissionChecker?.filterAllowedUsers(users) ?? [],
        getPermissionDeniedMessage: (action: string) => 
            permissionChecker?.getPermissionDeniedMessage(action) ?? 'Access denied.',
    };
};

/**
 * Hook for checking permissions on a specific user
 */
export const useUserPermissionsForTarget = (targetUser?: User) => {
    const userPermissions = useUserPermissions();
    
    return useMemo(() => {
        if (!targetUser || !userPermissions.permissionChecker) {
            return {
                canView: false,
                canEdit: false,
                canDelete: false,
                canChangeRole: false,
                canChangeStatus: false,
                canChangePassword: false,
            };
        }

        return {
            canView: userPermissions.canViewUser(targetUser),
            canEdit: userPermissions.canEditUser(targetUser),
            canDelete: userPermissions.canDeleteUser(targetUser),
            canChangeRole: userPermissions.canChangeUserRole(targetUser),
            canChangeStatus: userPermissions.canChangeUserStatus(targetUser),
            canChangePassword: userPermissions.canChangeUserPassword(targetUser),
        };
    }, [targetUser, userPermissions.permissionChecker]);
};

export default useUserPermissions;
