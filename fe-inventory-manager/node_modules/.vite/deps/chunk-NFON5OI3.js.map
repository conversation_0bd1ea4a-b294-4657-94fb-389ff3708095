{"version": 3, "sources": ["../../@mui/icons-material/esm/BarChart.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 9h4v11H4zm12 4h4v7h-4zm-6-9h4v16h-4z\"\n}), 'Bar<PERSON>hart');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,mBAAQ,kBAA2B,mBAAAA,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,UAAU;", "names": ["_jsx"]}