{"version": 3, "sources": ["../../object-keys/isArguments.js", "../../object-keys/implementation.js", "../../object-keys/index.js", "../../es-define-property/index.js", "../../es-errors/syntax.js", "../../es-errors/type.js", "../../gopd/gOPD.js", "../../gopd/index.js", "../../define-data-property/index.js", "../../has-property-descriptors/index.js", "../../define-properties/index.js", "../../es-object-atoms/index.js", "../../es-errors/index.js", "../../es-errors/eval.js", "../../es-errors/range.js", "../../es-errors/ref.js", "../../es-errors/uri.js", "../../math-intrinsics/abs.js", "../../math-intrinsics/floor.js", "../../math-intrinsics/max.js", "../../math-intrinsics/min.js", "../../math-intrinsics/pow.js", "../../math-intrinsics/round.js", "../../math-intrinsics/isNaN.js", "../../math-intrinsics/sign.js", "../../has-symbols/shams.js", "../../has-symbols/index.js", "../../get-proto/Reflect.getPrototypeOf.js", "../../get-proto/Object.getPrototypeOf.js", "../../function-bind/implementation.js", "../../function-bind/index.js", "../../call-bind-apply-helpers/functionCall.js", "../../call-bind-apply-helpers/functionApply.js", "../../call-bind-apply-helpers/reflectApply.js", "../../call-bind-apply-helpers/actualApply.js", "../../call-bind-apply-helpers/index.js", "../../dunder-proto/get.js", "../../get-proto/index.js", "../../hasown/index.js", "../../get-intrinsic/index.js", "../../set-function-length/index.js", "../../call-bind-apply-helpers/applyBind.js", "../../call-bind/index.js", "../../es-object-atoms/RequireObjectCoercible.js", "../../call-bound/index.js", "../../object.entries/implementation.js", "../../object.entries/polyfill.js", "../../object.entries/shim.js", "../../object.entries/index.js", "../../warning/warning.js", "../../node-polyglot/index.js", "../../ra-i18n-polyglot/src/index.ts"], "sourcesContent": ["'use strict';\n\nvar toStr = Object.prototype.toString;\n\nmodule.exports = function isArguments(value) {\n\tvar str = toStr.call(value);\n\tvar isArgs = str === '[object Arguments]';\n\tif (!isArgs) {\n\t\tisArgs = str !== '[object Array]' &&\n\t\t\tvalue !== null &&\n\t\t\ttypeof value === 'object' &&\n\t\t\ttypeof value.length === 'number' &&\n\t\t\tvalue.length >= 0 &&\n\t\t\ttoStr.call(value.callee) === '[object Function]';\n\t}\n\treturn isArgs;\n};\n", "'use strict';\n\nvar keysShim;\nif (!Object.keys) {\n\t// modified from https://github.com/es-shims/es5-shim\n\tvar has = Object.prototype.hasOwnProperty;\n\tvar toStr = Object.prototype.toString;\n\tvar isArgs = require('./isArguments'); // eslint-disable-line global-require\n\tvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\tvar hasDontEnumBug = !isEnumerable.call({ toString: null }, 'toString');\n\tvar hasProtoEnumBug = isEnumerable.call(function () {}, 'prototype');\n\tvar dontEnums = [\n\t\t'toString',\n\t\t'toLocaleString',\n\t\t'valueOf',\n\t\t'hasOwnProperty',\n\t\t'isPrototypeOf',\n\t\t'propertyIsEnumerable',\n\t\t'constructor'\n\t];\n\tvar equalsConstructorPrototype = function (o) {\n\t\tvar ctor = o.constructor;\n\t\treturn ctor && ctor.prototype === o;\n\t};\n\tvar excludedKeys = {\n\t\t$applicationCache: true,\n\t\t$console: true,\n\t\t$external: true,\n\t\t$frame: true,\n\t\t$frameElement: true,\n\t\t$frames: true,\n\t\t$innerHeight: true,\n\t\t$innerWidth: true,\n\t\t$onmozfullscreenchange: true,\n\t\t$onmozfullscreenerror: true,\n\t\t$outerHeight: true,\n\t\t$outerWidth: true,\n\t\t$pageXOffset: true,\n\t\t$pageYOffset: true,\n\t\t$parent: true,\n\t\t$scrollLeft: true,\n\t\t$scrollTop: true,\n\t\t$scrollX: true,\n\t\t$scrollY: true,\n\t\t$self: true,\n\t\t$webkitIndexedDB: true,\n\t\t$webkitStorageInfo: true,\n\t\t$window: true\n\t};\n\tvar hasAutomationEqualityBug = (function () {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined') { return false; }\n\t\tfor (var k in window) {\n\t\t\ttry {\n\t\t\t\tif (!excludedKeys['$' + k] && has.call(window, k) && window[k] !== null && typeof window[k] === 'object') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tequalsConstructorPrototype(window[k]);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}());\n\tvar equalsConstructorPrototypeIfNotBuggy = function (o) {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t}\n\t\ttry {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t};\n\n\tkeysShim = function keys(object) {\n\t\tvar isObject = object !== null && typeof object === 'object';\n\t\tvar isFunction = toStr.call(object) === '[object Function]';\n\t\tvar isArguments = isArgs(object);\n\t\tvar isString = isObject && toStr.call(object) === '[object String]';\n\t\tvar theKeys = [];\n\n\t\tif (!isObject && !isFunction && !isArguments) {\n\t\t\tthrow new TypeError('Object.keys called on a non-object');\n\t\t}\n\n\t\tvar skipProto = hasProtoEnumBug && isFunction;\n\t\tif (isString && object.length > 0 && !has.call(object, 0)) {\n\t\t\tfor (var i = 0; i < object.length; ++i) {\n\t\t\t\ttheKeys.push(String(i));\n\t\t\t}\n\t\t}\n\n\t\tif (isArguments && object.length > 0) {\n\t\t\tfor (var j = 0; j < object.length; ++j) {\n\t\t\t\ttheKeys.push(String(j));\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var name in object) {\n\t\t\t\tif (!(skipProto && name === 'prototype') && has.call(object, name)) {\n\t\t\t\t\ttheKeys.push(String(name));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (hasDontEnumBug) {\n\t\t\tvar skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n\n\t\t\tfor (var k = 0; k < dontEnums.length; ++k) {\n\t\t\t\tif (!(skipConstructor && dontEnums[k] === 'constructor') && has.call(object, dontEnums[k])) {\n\t\t\t\t\ttheKeys.push(dontEnums[k]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn theKeys;\n\t};\n}\nmodule.exports = keysShim;\n", "'use strict';\n\nvar slice = Array.prototype.slice;\nvar isArgs = require('./isArguments');\n\nvar origKeys = Object.keys;\nvar keysShim = origKeys ? function keys(o) { return origKeys(o); } : require('./implementation');\n\nvar originalKeys = Object.keys;\n\nkeysShim.shim = function shimObjectKeys() {\n\tif (Object.keys) {\n\t\tvar keysWorksWithArguments = (function () {\n\t\t\t// Safari 5.0 bug\n\t\t\tvar args = Object.keys(arguments);\n\t\t\treturn args && args.length === arguments.length;\n\t\t}(1, 2));\n\t\tif (!keysWorksWithArguments) {\n\t\t\tObject.keys = function keys(object) { // eslint-disable-line func-name-matching\n\t\t\t\tif (isArgs(object)) {\n\t\t\t\t\treturn originalKeys(slice.call(object));\n\t\t\t\t}\n\t\t\t\treturn originalKeys(object);\n\t\t\t};\n\t\t}\n\t} else {\n\t\tObject.keys = keysShim;\n\t}\n\treturn Object.keys || keysShim;\n};\n\nmodule.exports = keysShim;\n", "'use strict';\n\n/** @type {import('.')} */\nvar $defineProperty = Object.defineProperty || false;\nif ($defineProperty) {\n\ttry {\n\t\t$defineProperty({}, 'a', { value: 1 });\n\t} catch (e) {\n\t\t// IE 8 has a broken defineProperty\n\t\t$defineProperty = false;\n\t}\n}\n\nmodule.exports = $defineProperty;\n", "'use strict';\n\n/** @type {import('./syntax')} */\nmodule.exports = SyntaxError;\n", "'use strict';\n\n/** @type {import('./type')} */\nmodule.exports = TypeError;\n", "'use strict';\n\n/** @type {import('./gOPD')} */\nmodule.exports = Object.getOwnPropertyDescriptor;\n", "'use strict';\n\n/** @type {import('.')} */\nvar $gOPD = require('./gOPD');\n\nif ($gOPD) {\n\ttry {\n\t\t$gOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\t$gOPD = null;\n\t}\n}\n\nmodule.exports = $gOPD;\n", "'use strict';\n\nvar $defineProperty = require('es-define-property');\n\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\n\nvar gopd = require('gopd');\n\n/** @type {import('.')} */\nmodule.exports = function defineDataProperty(\n\tobj,\n\tproperty,\n\tvalue\n) {\n\tif (!obj || (typeof obj !== 'object' && typeof obj !== 'function')) {\n\t\tthrow new $TypeError('`obj` must be an object or a function`');\n\t}\n\tif (typeof property !== 'string' && typeof property !== 'symbol') {\n\t\tthrow new $TypeError('`property` must be a string or a symbol`');\n\t}\n\tif (arguments.length > 3 && typeof arguments[3] !== 'boolean' && arguments[3] !== null) {\n\t\tthrow new $TypeError('`nonEnumerable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 4 && typeof arguments[4] !== 'boolean' && arguments[4] !== null) {\n\t\tthrow new $TypeError('`nonWritable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 5 && typeof arguments[5] !== 'boolean' && arguments[5] !== null) {\n\t\tthrow new $TypeError('`nonConfigurable`, if provided, must be a boolean or null');\n\t}\n\tif (arguments.length > 6 && typeof arguments[6] !== 'boolean') {\n\t\tthrow new $TypeError('`loose`, if provided, must be a boolean');\n\t}\n\n\tvar nonEnumerable = arguments.length > 3 ? arguments[3] : null;\n\tvar nonWritable = arguments.length > 4 ? arguments[4] : null;\n\tvar nonConfigurable = arguments.length > 5 ? arguments[5] : null;\n\tvar loose = arguments.length > 6 ? arguments[6] : false;\n\n\t/* @type {false | TypedPropertyDescriptor<unknown>} */\n\tvar desc = !!gopd && gopd(obj, property);\n\n\tif ($defineProperty) {\n\t\t$defineProperty(obj, property, {\n\t\t\tconfigurable: nonConfigurable === null && desc ? desc.configurable : !nonConfigurable,\n\t\t\tenumerable: nonEnumerable === null && desc ? desc.enumerable : !nonEnumerable,\n\t\t\tvalue: value,\n\t\t\twritable: nonWritable === null && desc ? desc.writable : !nonWritable\n\t\t});\n\t} else if (loose || (!nonEnumerable && !nonWritable && !nonConfigurable)) {\n\t\t// must fall back to [[Set]], and was not explicitly asked to make non-enumerable, non-writable, or non-configurable\n\t\tobj[property] = value; // eslint-disable-line no-param-reassign\n\t} else {\n\t\tthrow new $SyntaxError('This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.');\n\t}\n};\n", "'use strict';\n\nvar $defineProperty = require('es-define-property');\n\nvar hasPropertyDescriptors = function hasPropertyDescriptors() {\n\treturn !!$defineProperty;\n};\n\nhasPropertyDescriptors.hasArrayLengthDefineBug = function hasArrayLengthDefineBug() {\n\t// node v0.6 has a bug where array lengths can be Set but not Defined\n\tif (!$defineProperty) {\n\t\treturn null;\n\t}\n\ttry {\n\t\treturn $defineProperty([], 'length', { value: 1 }).length !== 1;\n\t} catch (e) {\n\t\t// In Firefox 4-22, defining length on an array throws an exception.\n\t\treturn true;\n\t}\n};\n\nmodule.exports = hasPropertyDescriptors;\n", "'use strict';\n\nvar keys = require('object-keys');\nvar hasSymbols = typeof Symbol === 'function' && typeof Symbol('foo') === 'symbol';\n\nvar toStr = Object.prototype.toString;\nvar concat = Array.prototype.concat;\nvar defineDataProperty = require('define-data-property');\n\nvar isFunction = function (fn) {\n\treturn typeof fn === 'function' && toStr.call(fn) === '[object Function]';\n};\n\nvar supportsDescriptors = require('has-property-descriptors')();\n\nvar defineProperty = function (object, name, value, predicate) {\n\tif (name in object) {\n\t\tif (predicate === true) {\n\t\t\tif (object[name] === value) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t} else if (!isFunction(predicate) || !predicate()) {\n\t\t\treturn;\n\t\t}\n\t}\n\n\tif (supportsDescriptors) {\n\t\tdefineDataProperty(object, name, value, true);\n\t} else {\n\t\tdefineDataProperty(object, name, value);\n\t}\n};\n\nvar defineProperties = function (object, map) {\n\tvar predicates = arguments.length > 2 ? arguments[2] : {};\n\tvar props = keys(map);\n\tif (hasSymbols) {\n\t\tprops = concat.call(props, Object.getOwnPropertySymbols(map));\n\t}\n\tfor (var i = 0; i < props.length; i += 1) {\n\t\tdefineProperty(object, props[i], map[props[i]], predicates[props[i]]);\n\t}\n};\n\ndefineProperties.supportsDescriptors = !!supportsDescriptors;\n\nmodule.exports = defineProperties;\n", "'use strict';\n\n/** @type {import('.')} */\nmodule.exports = Object;\n", "'use strict';\n\n/** @type {import('.')} */\nmodule.exports = Error;\n", "'use strict';\n\n/** @type {import('./eval')} */\nmodule.exports = EvalError;\n", "'use strict';\n\n/** @type {import('./range')} */\nmodule.exports = RangeError;\n", "'use strict';\n\n/** @type {import('./ref')} */\nmodule.exports = ReferenceError;\n", "'use strict';\n\n/** @type {import('./uri')} */\nmodule.exports = URIError;\n", "'use strict';\n\n/** @type {import('./abs')} */\nmodule.exports = Math.abs;\n", "'use strict';\n\n/** @type {import('./floor')} */\nmodule.exports = Math.floor;\n", "'use strict';\n\n/** @type {import('./max')} */\nmodule.exports = Math.max;\n", "'use strict';\n\n/** @type {import('./min')} */\nmodule.exports = Math.min;\n", "'use strict';\n\n/** @type {import('./pow')} */\nmodule.exports = Math.pow;\n", "'use strict';\n\n/** @type {import('./round')} */\nmodule.exports = Math.round;\n", "'use strict';\n\n/** @type {import('./isNaN')} */\nmodule.exports = Number.isNaN || function isNaN(a) {\n\treturn a !== a;\n};\n", "'use strict';\n\nvar $isNaN = require('./isNaN');\n\n/** @type {import('./sign')} */\nmodule.exports = function sign(number) {\n\tif ($isNaN(number) || number === 0) {\n\t\treturn number;\n\t}\n\treturn number < 0 ? -1 : +1;\n};\n", "'use strict';\n\n/** @type {import('./shams')} */\n/* eslint complexity: [2, 18], max-statements: [2, 33] */\nmodule.exports = function hasSymbols() {\n\tif (typeof Symbol !== 'function' || typeof Object.getOwnPropertySymbols !== 'function') { return false; }\n\tif (typeof Symbol.iterator === 'symbol') { return true; }\n\n\t/** @type {{ [k in symbol]?: unknown }} */\n\tvar obj = {};\n\tvar sym = Symbol('test');\n\tvar symObj = Object(sym);\n\tif (typeof sym === 'string') { return false; }\n\n\tif (Object.prototype.toString.call(sym) !== '[object Symbol]') { return false; }\n\tif (Object.prototype.toString.call(symObj) !== '[object Symbol]') { return false; }\n\n\t// temp disabled per https://github.com/ljharb/object.assign/issues/17\n\t// if (sym instanceof Symbol) { return false; }\n\t// temp disabled per https://github.com/WebReflection/get-own-property-symbols/issues/4\n\t// if (!(symObj instanceof Symbol)) { return false; }\n\n\t// if (typeof Symbol.prototype.toString !== 'function') { return false; }\n\t// if (String(sym) !== Symbol.prototype.toString.call(sym)) { return false; }\n\n\tvar symVal = 42;\n\tobj[sym] = symVal;\n\tfor (var _ in obj) { return false; } // eslint-disable-line no-restricted-syntax, no-unreachable-loop\n\tif (typeof Object.keys === 'function' && Object.keys(obj).length !== 0) { return false; }\n\n\tif (typeof Object.getOwnPropertyNames === 'function' && Object.getOwnPropertyNames(obj).length !== 0) { return false; }\n\n\tvar syms = Object.getOwnPropertySymbols(obj);\n\tif (syms.length !== 1 || syms[0] !== sym) { return false; }\n\n\tif (!Object.prototype.propertyIsEnumerable.call(obj, sym)) { return false; }\n\n\tif (typeof Object.getOwnPropertyDescriptor === 'function') {\n\t\t// eslint-disable-next-line no-extra-parens\n\t\tvar descriptor = /** @type {PropertyDescriptor} */ (Object.getOwnPropertyDescriptor(obj, sym));\n\t\tif (descriptor.value !== symVal || descriptor.enumerable !== true) { return false; }\n\t}\n\n\treturn true;\n};\n", "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n", "'use strict';\n\n/** @type {import('./Reflect.getPrototypeOf')} */\nmodule.exports = (typeof Reflect !== 'undefined' && Reflect.getPrototypeOf) || null;\n", "'use strict';\n\nvar $Object = require('es-object-atoms');\n\n/** @type {import('./Object.getPrototypeOf')} */\nmodule.exports = $Object.getPrototypeOf || null;\n", "'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\n\nvar concatty = function concatty(a, b) {\n    var arr = [];\n\n    for (var i = 0; i < a.length; i += 1) {\n        arr[i] = a[i];\n    }\n    for (var j = 0; j < b.length; j += 1) {\n        arr[j + a.length] = b[j];\n    }\n\n    return arr;\n};\n\nvar slicy = function slicy(arrLike, offset) {\n    var arr = [];\n    for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n        arr[j] = arrLike[i];\n    }\n    return arr;\n};\n\nvar joiny = function (arr, joiner) {\n    var str = '';\n    for (var i = 0; i < arr.length; i += 1) {\n        str += arr[i];\n        if (i + 1 < arr.length) {\n            str += joiner;\n        }\n    }\n    return str;\n};\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slicy(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                concatty(args, arguments)\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        }\n        return target.apply(\n            that,\n            concatty(args, arguments)\n        );\n\n    };\n\n    var boundLength = max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs[i] = '$' + i;\n    }\n\n    bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n", "'use strict';\n\n/** @type {import('./functionCall')} */\nmodule.exports = Function.prototype.call;\n", "'use strict';\n\n/** @type {import('./functionApply')} */\nmodule.exports = Function.prototype.apply;\n", "'use strict';\n\n/** @type {import('./reflectApply')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect && Reflect.apply;\n", "'use strict';\n\nvar bind = require('function-bind');\n\nvar $apply = require('./functionApply');\nvar $call = require('./functionCall');\nvar $reflectApply = require('./reflectApply');\n\n/** @type {import('./actualApply')} */\nmodule.exports = $reflectApply || bind.call($call, $apply);\n", "'use strict';\n\nvar bind = require('function-bind');\nvar $TypeError = require('es-errors/type');\n\nvar $call = require('./functionCall');\nvar $actualApply = require('./actualApply');\n\n/** @type {(args: [Function, thisArg?: unknown, ...args: unknown[]]) => Function} TODO FIXME, find a way to use import('.') */\nmodule.exports = function callBindBasic(args) {\n\tif (args.length < 1 || typeof args[0] !== 'function') {\n\t\tthrow new $TypeError('a function is required');\n\t}\n\treturn $actualApply(bind, $call, args);\n};\n", "'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\nvar hasProtoAccessor;\ntry {\n\t// eslint-disable-next-line no-extra-parens, no-proto\n\thasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */ ([]).__proto__ === Array.prototype;\n} catch (e) {\n\tif (!e || typeof e !== 'object' || !('code' in e) || e.code !== 'ERR_PROTO_ACCESS') {\n\t\tthrow e;\n\t}\n}\n\n// eslint-disable-next-line no-extra-parens\nvar desc = !!hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */ ('__proto__'));\n\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function'\n\t? callBind([desc.get])\n\t: typeof $getPrototypeOf === 'function'\n\t\t? /** @type {import('./get')} */ function getDunder(value) {\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\treturn $getPrototypeOf(value == null ? value : $Object(value));\n\t\t}\n\t\t: false;\n", "'use strict';\n\nvar reflectGetProto = require('./Reflect.getPrototypeOf');\nvar originalGetProto = require('./Object.getPrototypeOf');\n\nvar getDunderProto = require('dunder-proto/get');\n\n/** @type {import('.')} */\nmodule.exports = reflectGetProto\n\t? function getProto(O) {\n\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\treturn reflectGetProto(O);\n\t}\n\t: originalGetProto\n\t\t? function getProto(O) {\n\t\t\tif (!O || (typeof O !== 'object' && typeof O !== 'function')) {\n\t\t\t\tthrow new TypeError('getProto: not an object');\n\t\t\t}\n\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\treturn originalGetProto(O);\n\t\t}\n\t\t: getDunderProto\n\t\t\t? function getProto(O) {\n\t\t\t\t// @ts-expect-error TS can't narrow inside a closure, for some reason\n\t\t\t\treturn getDunderProto(O);\n\t\t\t}\n\t\t\t: null;\n", "'use strict';\n\nvar call = Function.prototype.call;\nvar $hasOwn = Object.prototype.hasOwnProperty;\nvar bind = require('function-bind');\n\n/** @type {import('.')} */\nmodule.exports = bind.call(call, $hasOwn);\n", "'use strict';\n\nvar undefined;\n\nvar $Object = require('es-object-atoms');\n\nvar $Error = require('es-errors');\nvar $EvalError = require('es-errors/eval');\nvar $RangeError = require('es-errors/range');\nvar $ReferenceError = require('es-errors/ref');\nvar $SyntaxError = require('es-errors/syntax');\nvar $TypeError = require('es-errors/type');\nvar $URIError = require('es-errors/uri');\n\nvar abs = require('math-intrinsics/abs');\nvar floor = require('math-intrinsics/floor');\nvar max = require('math-intrinsics/max');\nvar min = require('math-intrinsics/min');\nvar pow = require('math-intrinsics/pow');\nvar round = require('math-intrinsics/round');\nvar sign = require('math-intrinsics/sign');\n\nvar $Function = Function;\n\n// eslint-disable-next-line consistent-return\nvar getEvalledConstructor = function (expressionSyntax) {\n\ttry {\n\t\treturn $Function('\"use strict\"; return (' + expressionSyntax + ').constructor;')();\n\t} catch (e) {}\n};\n\nvar $gOPD = require('gopd');\nvar $defineProperty = require('es-define-property');\n\nvar throwTypeError = function () {\n\tthrow new $TypeError();\n};\nvar ThrowTypeError = $gOPD\n\t? (function () {\n\t\ttry {\n\t\t\t// eslint-disable-next-line no-unused-expressions, no-caller, no-restricted-properties\n\t\t\targuments.callee; // IE 8 does not throw here\n\t\t\treturn throwTypeError;\n\t\t} catch (calleeThrows) {\n\t\t\ttry {\n\t\t\t\t// IE 8 throws on Object.getOwnPropertyDescriptor(arguments, '')\n\t\t\t\treturn $gOPD(arguments, 'callee').get;\n\t\t\t} catch (gOPDthrows) {\n\t\t\t\treturn throwTypeError;\n\t\t\t}\n\t\t}\n\t}())\n\t: throwTypeError;\n\nvar hasSymbols = require('has-symbols')();\n\nvar getProto = require('get-proto');\nvar $ObjectGPO = require('get-proto/Object.getPrototypeOf');\nvar $ReflectGPO = require('get-proto/Reflect.getPrototypeOf');\n\nvar $apply = require('call-bind-apply-helpers/functionApply');\nvar $call = require('call-bind-apply-helpers/functionCall');\n\nvar needsEval = {};\n\nvar TypedArray = typeof Uint8Array === 'undefined' || !getProto ? undefined : getProto(Uint8Array);\n\nvar INTRINSICS = {\n\t__proto__: null,\n\t'%AggregateError%': typeof AggregateError === 'undefined' ? undefined : AggregateError,\n\t'%Array%': Array,\n\t'%ArrayBuffer%': typeof ArrayBuffer === 'undefined' ? undefined : ArrayBuffer,\n\t'%ArrayIteratorPrototype%': hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined,\n\t'%AsyncFromSyncIteratorPrototype%': undefined,\n\t'%AsyncFunction%': needsEval,\n\t'%AsyncGenerator%': needsEval,\n\t'%AsyncGeneratorFunction%': needsEval,\n\t'%AsyncIteratorPrototype%': needsEval,\n\t'%Atomics%': typeof Atomics === 'undefined' ? undefined : Atomics,\n\t'%BigInt%': typeof BigInt === 'undefined' ? undefined : BigInt,\n\t'%BigInt64Array%': typeof BigInt64Array === 'undefined' ? undefined : BigInt64Array,\n\t'%BigUint64Array%': typeof BigUint64Array === 'undefined' ? undefined : BigUint64Array,\n\t'%Boolean%': Boolean,\n\t'%DataView%': typeof DataView === 'undefined' ? undefined : DataView,\n\t'%Date%': Date,\n\t'%decodeURI%': decodeURI,\n\t'%decodeURIComponent%': decodeURIComponent,\n\t'%encodeURI%': encodeURI,\n\t'%encodeURIComponent%': encodeURIComponent,\n\t'%Error%': $Error,\n\t'%eval%': eval, // eslint-disable-line no-eval\n\t'%EvalError%': $EvalError,\n\t'%Float16Array%': typeof Float16Array === 'undefined' ? undefined : Float16Array,\n\t'%Float32Array%': typeof Float32Array === 'undefined' ? undefined : Float32Array,\n\t'%Float64Array%': typeof Float64Array === 'undefined' ? undefined : Float64Array,\n\t'%FinalizationRegistry%': typeof FinalizationRegistry === 'undefined' ? undefined : FinalizationRegistry,\n\t'%Function%': $Function,\n\t'%GeneratorFunction%': needsEval,\n\t'%Int8Array%': typeof Int8Array === 'undefined' ? undefined : Int8Array,\n\t'%Int16Array%': typeof Int16Array === 'undefined' ? undefined : Int16Array,\n\t'%Int32Array%': typeof Int32Array === 'undefined' ? undefined : Int32Array,\n\t'%isFinite%': isFinite,\n\t'%isNaN%': isNaN,\n\t'%IteratorPrototype%': hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined,\n\t'%JSON%': typeof JSON === 'object' ? JSON : undefined,\n\t'%Map%': typeof Map === 'undefined' ? undefined : Map,\n\t'%MapIteratorPrototype%': typeof Map === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Map()[Symbol.iterator]()),\n\t'%Math%': Math,\n\t'%Number%': Number,\n\t'%Object%': $Object,\n\t'%Object.getOwnPropertyDescriptor%': $gOPD,\n\t'%parseFloat%': parseFloat,\n\t'%parseInt%': parseInt,\n\t'%Promise%': typeof Promise === 'undefined' ? undefined : Promise,\n\t'%Proxy%': typeof Proxy === 'undefined' ? undefined : Proxy,\n\t'%RangeError%': $RangeError,\n\t'%ReferenceError%': $ReferenceError,\n\t'%Reflect%': typeof Reflect === 'undefined' ? undefined : Reflect,\n\t'%RegExp%': RegExp,\n\t'%Set%': typeof Set === 'undefined' ? undefined : Set,\n\t'%SetIteratorPrototype%': typeof Set === 'undefined' || !hasSymbols || !getProto ? undefined : getProto(new Set()[Symbol.iterator]()),\n\t'%SharedArrayBuffer%': typeof SharedArrayBuffer === 'undefined' ? undefined : SharedArrayBuffer,\n\t'%String%': String,\n\t'%StringIteratorPrototype%': hasSymbols && getProto ? getProto(''[Symbol.iterator]()) : undefined,\n\t'%Symbol%': hasSymbols ? Symbol : undefined,\n\t'%SyntaxError%': $SyntaxError,\n\t'%ThrowTypeError%': ThrowTypeError,\n\t'%TypedArray%': TypedArray,\n\t'%TypeError%': $TypeError,\n\t'%Uint8Array%': typeof Uint8Array === 'undefined' ? undefined : Uint8Array,\n\t'%Uint8ClampedArray%': typeof Uint8ClampedArray === 'undefined' ? undefined : Uint8ClampedArray,\n\t'%Uint16Array%': typeof Uint16Array === 'undefined' ? undefined : Uint16Array,\n\t'%Uint32Array%': typeof Uint32Array === 'undefined' ? undefined : Uint32Array,\n\t'%URIError%': $URIError,\n\t'%WeakMap%': typeof WeakMap === 'undefined' ? undefined : WeakMap,\n\t'%WeakRef%': typeof WeakRef === 'undefined' ? undefined : WeakRef,\n\t'%WeakSet%': typeof WeakSet === 'undefined' ? undefined : WeakSet,\n\n\t'%Function.prototype.call%': $call,\n\t'%Function.prototype.apply%': $apply,\n\t'%Object.defineProperty%': $defineProperty,\n\t'%Object.getPrototypeOf%': $ObjectGPO,\n\t'%Math.abs%': abs,\n\t'%Math.floor%': floor,\n\t'%Math.max%': max,\n\t'%Math.min%': min,\n\t'%Math.pow%': pow,\n\t'%Math.round%': round,\n\t'%Math.sign%': sign,\n\t'%Reflect.getPrototypeOf%': $ReflectGPO\n};\n\nif (getProto) {\n\ttry {\n\t\tnull.error; // eslint-disable-line no-unused-expressions\n\t} catch (e) {\n\t\t// https://github.com/tc39/proposal-shadowrealm/pull/384#issuecomment-1364264229\n\t\tvar errorProto = getProto(getProto(e));\n\t\tINTRINSICS['%Error.prototype%'] = errorProto;\n\t}\n}\n\nvar doEval = function doEval(name) {\n\tvar value;\n\tif (name === '%AsyncFunction%') {\n\t\tvalue = getEvalledConstructor('async function () {}');\n\t} else if (name === '%GeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('function* () {}');\n\t} else if (name === '%AsyncGeneratorFunction%') {\n\t\tvalue = getEvalledConstructor('async function* () {}');\n\t} else if (name === '%AsyncGenerator%') {\n\t\tvar fn = doEval('%AsyncGeneratorFunction%');\n\t\tif (fn) {\n\t\t\tvalue = fn.prototype;\n\t\t}\n\t} else if (name === '%AsyncIteratorPrototype%') {\n\t\tvar gen = doEval('%AsyncGenerator%');\n\t\tif (gen && getProto) {\n\t\t\tvalue = getProto(gen.prototype);\n\t\t}\n\t}\n\n\tINTRINSICS[name] = value;\n\n\treturn value;\n};\n\nvar LEGACY_ALIASES = {\n\t__proto__: null,\n\t'%ArrayBufferPrototype%': ['ArrayBuffer', 'prototype'],\n\t'%ArrayPrototype%': ['Array', 'prototype'],\n\t'%ArrayProto_entries%': ['Array', 'prototype', 'entries'],\n\t'%ArrayProto_forEach%': ['Array', 'prototype', 'forEach'],\n\t'%ArrayProto_keys%': ['Array', 'prototype', 'keys'],\n\t'%ArrayProto_values%': ['Array', 'prototype', 'values'],\n\t'%AsyncFunctionPrototype%': ['AsyncFunction', 'prototype'],\n\t'%AsyncGenerator%': ['AsyncGeneratorFunction', 'prototype'],\n\t'%AsyncGeneratorPrototype%': ['AsyncGeneratorFunction', 'prototype', 'prototype'],\n\t'%BooleanPrototype%': ['Boolean', 'prototype'],\n\t'%DataViewPrototype%': ['DataView', 'prototype'],\n\t'%DatePrototype%': ['Date', 'prototype'],\n\t'%ErrorPrototype%': ['Error', 'prototype'],\n\t'%EvalErrorPrototype%': ['EvalError', 'prototype'],\n\t'%Float32ArrayPrototype%': ['Float32Array', 'prototype'],\n\t'%Float64ArrayPrototype%': ['Float64Array', 'prototype'],\n\t'%FunctionPrototype%': ['Function', 'prototype'],\n\t'%Generator%': ['GeneratorFunction', 'prototype'],\n\t'%GeneratorPrototype%': ['GeneratorFunction', 'prototype', 'prototype'],\n\t'%Int8ArrayPrototype%': ['Int8Array', 'prototype'],\n\t'%Int16ArrayPrototype%': ['Int16Array', 'prototype'],\n\t'%Int32ArrayPrototype%': ['Int32Array', 'prototype'],\n\t'%JSONParse%': ['JSON', 'parse'],\n\t'%JSONStringify%': ['JSON', 'stringify'],\n\t'%MapPrototype%': ['Map', 'prototype'],\n\t'%NumberPrototype%': ['Number', 'prototype'],\n\t'%ObjectPrototype%': ['Object', 'prototype'],\n\t'%ObjProto_toString%': ['Object', 'prototype', 'toString'],\n\t'%ObjProto_valueOf%': ['Object', 'prototype', 'valueOf'],\n\t'%PromisePrototype%': ['Promise', 'prototype'],\n\t'%PromiseProto_then%': ['Promise', 'prototype', 'then'],\n\t'%Promise_all%': ['Promise', 'all'],\n\t'%Promise_reject%': ['Promise', 'reject'],\n\t'%Promise_resolve%': ['Promise', 'resolve'],\n\t'%RangeErrorPrototype%': ['RangeError', 'prototype'],\n\t'%ReferenceErrorPrototype%': ['ReferenceError', 'prototype'],\n\t'%RegExpPrototype%': ['RegExp', 'prototype'],\n\t'%SetPrototype%': ['Set', 'prototype'],\n\t'%SharedArrayBufferPrototype%': ['SharedArrayBuffer', 'prototype'],\n\t'%StringPrototype%': ['String', 'prototype'],\n\t'%SymbolPrototype%': ['Symbol', 'prototype'],\n\t'%SyntaxErrorPrototype%': ['SyntaxError', 'prototype'],\n\t'%TypedArrayPrototype%': ['TypedArray', 'prototype'],\n\t'%TypeErrorPrototype%': ['TypeError', 'prototype'],\n\t'%Uint8ArrayPrototype%': ['Uint8Array', 'prototype'],\n\t'%Uint8ClampedArrayPrototype%': ['Uint8ClampedArray', 'prototype'],\n\t'%Uint16ArrayPrototype%': ['Uint16Array', 'prototype'],\n\t'%Uint32ArrayPrototype%': ['Uint32Array', 'prototype'],\n\t'%URIErrorPrototype%': ['URIError', 'prototype'],\n\t'%WeakMapPrototype%': ['WeakMap', 'prototype'],\n\t'%WeakSetPrototype%': ['WeakSet', 'prototype']\n};\n\nvar bind = require('function-bind');\nvar hasOwn = require('hasown');\nvar $concat = bind.call($call, Array.prototype.concat);\nvar $spliceApply = bind.call($apply, Array.prototype.splice);\nvar $replace = bind.call($call, String.prototype.replace);\nvar $strSlice = bind.call($call, String.prototype.slice);\nvar $exec = bind.call($call, RegExp.prototype.exec);\n\n/* adapted from https://github.com/lodash/lodash/blob/4.17.15/dist/lodash.js#L6735-L6744 */\nvar rePropName = /[^%.[\\]]+|\\[(?:(-?\\d+(?:\\.\\d+)?)|([\"'])((?:(?!\\2)[^\\\\]|\\\\.)*?)\\2)\\]|(?=(?:\\.|\\[\\])(?:\\.|\\[\\]|%$))/g;\nvar reEscapeChar = /\\\\(\\\\)?/g; /** Used to match backslashes in property paths. */\nvar stringToPath = function stringToPath(string) {\n\tvar first = $strSlice(string, 0, 1);\n\tvar last = $strSlice(string, -1);\n\tif (first === '%' && last !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected closing `%`');\n\t} else if (last === '%' && first !== '%') {\n\t\tthrow new $SyntaxError('invalid intrinsic syntax, expected opening `%`');\n\t}\n\tvar result = [];\n\t$replace(string, rePropName, function (match, number, quote, subString) {\n\t\tresult[result.length] = quote ? $replace(subString, reEscapeChar, '$1') : number || match;\n\t});\n\treturn result;\n};\n/* end adaptation */\n\nvar getBaseIntrinsic = function getBaseIntrinsic(name, allowMissing) {\n\tvar intrinsicName = name;\n\tvar alias;\n\tif (hasOwn(LEGACY_ALIASES, intrinsicName)) {\n\t\talias = LEGACY_ALIASES[intrinsicName];\n\t\tintrinsicName = '%' + alias[0] + '%';\n\t}\n\n\tif (hasOwn(INTRINSICS, intrinsicName)) {\n\t\tvar value = INTRINSICS[intrinsicName];\n\t\tif (value === needsEval) {\n\t\t\tvalue = doEval(intrinsicName);\n\t\t}\n\t\tif (typeof value === 'undefined' && !allowMissing) {\n\t\t\tthrow new $TypeError('intrinsic ' + name + ' exists, but is not available. Please file an issue!');\n\t\t}\n\n\t\treturn {\n\t\t\talias: alias,\n\t\t\tname: intrinsicName,\n\t\t\tvalue: value\n\t\t};\n\t}\n\n\tthrow new $SyntaxError('intrinsic ' + name + ' does not exist!');\n};\n\nmodule.exports = function GetIntrinsic(name, allowMissing) {\n\tif (typeof name !== 'string' || name.length === 0) {\n\t\tthrow new $TypeError('intrinsic name must be a non-empty string');\n\t}\n\tif (arguments.length > 1 && typeof allowMissing !== 'boolean') {\n\t\tthrow new $TypeError('\"allowMissing\" argument must be a boolean');\n\t}\n\n\tif ($exec(/^%?[^%]*%?$/, name) === null) {\n\t\tthrow new $SyntaxError('`%` may not be present anywhere but at the beginning and end of the intrinsic name');\n\t}\n\tvar parts = stringToPath(name);\n\tvar intrinsicBaseName = parts.length > 0 ? parts[0] : '';\n\n\tvar intrinsic = getBaseIntrinsic('%' + intrinsicBaseName + '%', allowMissing);\n\tvar intrinsicRealName = intrinsic.name;\n\tvar value = intrinsic.value;\n\tvar skipFurtherCaching = false;\n\n\tvar alias = intrinsic.alias;\n\tif (alias) {\n\t\tintrinsicBaseName = alias[0];\n\t\t$spliceApply(parts, $concat([0, 1], alias));\n\t}\n\n\tfor (var i = 1, isOwn = true; i < parts.length; i += 1) {\n\t\tvar part = parts[i];\n\t\tvar first = $strSlice(part, 0, 1);\n\t\tvar last = $strSlice(part, -1);\n\t\tif (\n\t\t\t(\n\t\t\t\t(first === '\"' || first === \"'\" || first === '`')\n\t\t\t\t|| (last === '\"' || last === \"'\" || last === '`')\n\t\t\t)\n\t\t\t&& first !== last\n\t\t) {\n\t\t\tthrow new $SyntaxError('property names with quotes must have matching quotes');\n\t\t}\n\t\tif (part === 'constructor' || !isOwn) {\n\t\t\tskipFurtherCaching = true;\n\t\t}\n\n\t\tintrinsicBaseName += '.' + part;\n\t\tintrinsicRealName = '%' + intrinsicBaseName + '%';\n\n\t\tif (hasOwn(INTRINSICS, intrinsicRealName)) {\n\t\t\tvalue = INTRINSICS[intrinsicRealName];\n\t\t} else if (value != null) {\n\t\t\tif (!(part in value)) {\n\t\t\t\tif (!allowMissing) {\n\t\t\t\t\tthrow new $TypeError('base intrinsic for ' + name + ' exists, but the property is not available.');\n\t\t\t\t}\n\t\t\t\treturn void undefined;\n\t\t\t}\n\t\t\tif ($gOPD && (i + 1) >= parts.length) {\n\t\t\t\tvar desc = $gOPD(value, part);\n\t\t\t\tisOwn = !!desc;\n\n\t\t\t\t// By convention, when a data property is converted to an accessor\n\t\t\t\t// property to emulate a data property that does not suffer from\n\t\t\t\t// the override mistake, that accessor's getter is marked with\n\t\t\t\t// an `originalValue` property. Here, when we detect this, we\n\t\t\t\t// uphold the illusion by pretending to see that original data\n\t\t\t\t// property, i.e., returning the value rather than the getter\n\t\t\t\t// itself.\n\t\t\t\tif (isOwn && 'get' in desc && !('originalValue' in desc.get)) {\n\t\t\t\t\tvalue = desc.get;\n\t\t\t\t} else {\n\t\t\t\t\tvalue = value[part];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tisOwn = hasOwn(value, part);\n\t\t\t\tvalue = value[part];\n\t\t\t}\n\n\t\t\tif (isOwn && !skipFurtherCaching) {\n\t\t\t\tINTRINSICS[intrinsicRealName] = value;\n\t\t\t}\n\t\t}\n\t}\n\treturn value;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar define = require('define-data-property');\nvar hasDescriptors = require('has-property-descriptors')();\nvar gOPD = require('gopd');\n\nvar $TypeError = require('es-errors/type');\nvar $floor = GetIntrinsic('%Math.floor%');\n\n/** @type {import('.')} */\nmodule.exports = function setFunctionLength(fn, length) {\n\tif (typeof fn !== 'function') {\n\t\tthrow new $TypeError('`fn` is not a function');\n\t}\n\tif (typeof length !== 'number' || length < 0 || length > 0xFFFFFFFF || $floor(length) !== length) {\n\t\tthrow new $TypeError('`length` must be a positive 32-bit integer');\n\t}\n\n\tvar loose = arguments.length > 2 && !!arguments[2];\n\n\tvar functionLengthIsConfigurable = true;\n\tvar functionLengthIsWritable = true;\n\tif ('length' in fn && gOPD) {\n\t\tvar desc = gOPD(fn, 'length');\n\t\tif (desc && !desc.configurable) {\n\t\t\tfunctionLengthIsConfigurable = false;\n\t\t}\n\t\tif (desc && !desc.writable) {\n\t\t\tfunctionLengthIsWritable = false;\n\t\t}\n\t}\n\n\tif (functionLengthIsConfigurable || functionLengthIsWritable || !loose) {\n\t\tif (hasDescriptors) {\n\t\t\tdefine(/** @type {Parameters<define>[0]} */ (fn), 'length', length, true, true);\n\t\t} else {\n\t\t\tdefine(/** @type {Parameters<define>[0]} */ (fn), 'length', length);\n\t\t}\n\t}\n\treturn fn;\n};\n", "'use strict';\n\nvar bind = require('function-bind');\nvar $apply = require('./functionApply');\nvar actualApply = require('./actualApply');\n\n/** @type {import('./applyBind')} */\nmodule.exports = function applyBind() {\n\treturn actualApply(bind, $apply, arguments);\n};\n", "'use strict';\n\nvar setFunctionLength = require('set-function-length');\n\nvar $defineProperty = require('es-define-property');\n\nvar callBindBasic = require('call-bind-apply-helpers');\nvar applyBind = require('call-bind-apply-helpers/applyBind');\n\nmodule.exports = function callBind(originalFunction) {\n\tvar func = callBindBasic(arguments);\n\tvar adjustedLength = originalFunction.length - (arguments.length - 1);\n\treturn setFunctionLength(\n\t\tfunc,\n\t\t1 + (adjustedLength > 0 ? adjustedLength : 0),\n\t\ttrue\n\t);\n};\n\nif ($defineProperty) {\n\t$defineProperty(module.exports, 'apply', { value: applyBind });\n} else {\n\tmodule.exports.apply = applyBind;\n}\n", "'use strict';\n\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('./RequireObjectCoercible')} */\nmodule.exports = function RequireObjectCoercible(value) {\n\tif (value == null) {\n\t\tthrow new $TypeError((arguments.length > 0 && arguments[1]) || ('Cannot call method on ' + value));\n\t}\n\treturn value;\n};\n", "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBindBasic = require('call-bind-apply-helpers');\n\n/** @type {(thisArg: string, searchString: string, position?: number) => number} */\nvar $indexOf = callBindBasic([GetIntrinsic('%String.prototype.indexOf%')]);\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\t/* eslint no-extra-parens: 0 */\n\n\tvar intrinsic = /** @type {(this: unknown, ...args: unknown[]) => unknown} */ (GetIntrinsic(name, !!allowMissing));\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBindBasic(/** @type {const} */ ([intrinsic]));\n\t}\n\treturn intrinsic;\n};\n", "'use strict';\n\nvar RequireObjectCoercible = require('es-object-atoms/RequireObjectCoercible');\nvar callBound = require('call-bound');\nvar $isEnumerable = callBound('Object.prototype.propertyIsEnumerable');\nvar $push = callBound('Array.prototype.push');\n\nmodule.exports = function entries(O) {\n\tvar obj = RequireObjectCoercible(O);\n\tvar entrys = [];\n\tfor (var key in obj) {\n\t\tif ($isEnumerable(obj, key)) { // checks own-ness as well\n\t\t\t$push(entrys, [key, obj[key]]);\n\t\t}\n\t}\n\treturn entrys;\n};\n", "'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = function getPolyfill() {\n\treturn typeof Object.entries === 'function' ? Object.entries : implementation;\n};\n", "'use strict';\n\nvar getPolyfill = require('./polyfill');\nvar define = require('define-properties');\n\nmodule.exports = function shimEntries() {\n\tvar polyfill = getPolyfill();\n\tdefine(Object, { entries: polyfill }, {\n\t\tentries: function testEntries() {\n\t\t\treturn Object.entries !== polyfill;\n\t\t}\n\t});\n\treturn polyfill;\n};\n", "'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\n\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\n\nvar polyfill = callBind(getPolyfill(), Object);\n\ndefine(polyfill, {\n\tgetPolyfill: getPolyfill,\n\timplementation: implementation,\n\tshim: shim\n});\n\nmodule.exports = polyfill;\n", "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n", "//     (c) 2012-2018 Airbnb, Inc.\n//\n//     polyglot.js may be freely distributed under the terms of the BSD\n//     license. For all licensing information, details, and documentation:\n//     http://airbnb.github.com/polyglot.js\n//\n//\n// Polyglot.js is an I18n helper library written in JavaScript, made to\n// work both in the browser and in Node. It provides a simple solution for\n// interpolation and pluralization, based off of Airbnb's\n// experience adding I18n functionality to its Backbone.js and Node apps.\n//\n// Polylglot is agnostic to your translation backend. It doesn't perform any\n// translation; it simply gives you a way to manage translated phrases from\n// your client- or server-side JavaScript application.\n//\n\n'use strict';\n\nvar entries = require('object.entries');\nvar warning = require('warning');\nvar has = require('hasown');\n\nvar warn = function warn(message) {\n  warning(false, message);\n};\n\nvar defaultReplace = String.prototype.replace;\nvar split = String.prototype.split;\n\n// #### Pluralization methods\n// The string that separates the different phrase possibilities.\nvar delimiter = '||||';\n\nvar russianPluralGroups = function (n) {\n  var lastTwo = n % 100;\n  var end = lastTwo % 10;\n  if (lastTwo !== 11 && end === 1) {\n    return 0;\n  }\n  if (2 <= end && end <= 4 && !(lastTwo >= 12 && lastTwo <= 14)) {\n    return 1;\n  }\n  return 2;\n};\n\nvar defaultPluralRules = {\n  // Mapping from pluralization group plural logic.\n  pluralTypes: {\n    arabic: function (n) {\n      // http://www.arabeyes.org/Plural_Forms\n      if (n < 3) { return n; }\n      var lastTwo = n % 100;\n      if (lastTwo >= 3 && lastTwo <= 10) return 3;\n      return lastTwo >= 11 ? 4 : 5;\n    },\n    bosnian_serbian: russianPluralGroups,\n    chinese: function () { return 0; },\n    croatian: russianPluralGroups,\n    french: function (n) { return n >= 2 ? 1 : 0; },\n    german: function (n) { return n !== 1 ? 1 : 0; },\n    russian: russianPluralGroups,\n    lithuanian: function (n) {\n      if (n % 10 === 1 && n % 100 !== 11) { return 0; }\n      return n % 10 >= 2 && n % 10 <= 9 && (n % 100 < 11 || n % 100 > 19) ? 1 : 2;\n    },\n    czech: function (n) {\n      if (n === 1) { return 0; }\n      return (n >= 2 && n <= 4) ? 1 : 2;\n    },\n    polish: function (n) {\n      if (n === 1) { return 0; }\n      var end = n % 10;\n      return 2 <= end && end <= 4 && (n % 100 < 10 || n % 100 >= 20) ? 1 : 2;\n    },\n    icelandic: function (n) { return (n % 10 !== 1 || n % 100 === 11) ? 1 : 0; },\n    slovenian: function (n) {\n      var lastTwo = n % 100;\n      if (lastTwo === 1) {\n        return 0;\n      }\n      if (lastTwo === 2) {\n        return 1;\n      }\n      if (lastTwo === 3 || lastTwo === 4) {\n        return 2;\n      }\n      return 3;\n    },\n    romanian: function (n) {\n      if (n === 1) { return 0; }\n      var lastTwo = n % 100;\n      if (n === 0 || (lastTwo >= 2 && lastTwo <= 19)) { return 1; }\n      return 2;\n    },\n    ukrainian: russianPluralGroups\n  },\n\n  // Mapping from pluralization group to individual language codes/locales.\n  // Will look up based on exact match, if not found and it's a locale will parse the locale\n  // for language code, and if that does not exist will default to 'en'\n  pluralTypeToLanguages: {\n    arabic: ['ar'],\n    bosnian_serbian: ['bs-Latn-BA', 'bs-Cyrl-BA', 'srl-RS', 'sr-RS'],\n    chinese: ['id', 'id-ID', 'ja', 'ko', 'ko-KR', 'lo', 'ms', 'th', 'th-TH', 'zh'],\n    croatian: ['hr', 'hr-HR'],\n    german: ['fa', 'da', 'de', 'en', 'es', 'fi', 'el', 'he', 'hi-IN', 'hu', 'hu-HU', 'it', 'nl', 'no', 'pt', 'sv', 'tr'],\n    french: ['fr', 'tl', 'pt-br'],\n    russian: ['ru', 'ru-RU'],\n    lithuanian: ['lt'],\n    czech: ['cs', 'cs-CZ', 'sk'],\n    polish: ['pl'],\n    icelandic: ['is', 'mk'],\n    slovenian: ['sl-SL'],\n    romanian: ['ro'],\n    ukrainian: ['uk', 'ua']\n  }\n};\n\nfunction langToTypeMap(mapping) {\n  var ret = {};\n  var mappingEntries = entries(mapping);\n  for (var i = 0; i < mappingEntries.length; i += 1) {\n    var type = mappingEntries[i][0];\n    var langs = mappingEntries[i][1];\n    for (var j = 0; j < langs.length; j += 1) {\n      ret[langs[j]] = type;\n    }\n  }\n  return ret;\n}\n\nfunction pluralTypeName(pluralRules, locale) {\n  var langToPluralType = langToTypeMap(pluralRules.pluralTypeToLanguages);\n  return langToPluralType[locale]\n    || langToPluralType[split.call(locale, /-/, 1)[0]]\n    || langToPluralType.en;\n}\n\nfunction pluralTypeIndex(pluralRules, pluralType, count) {\n  return pluralRules.pluralTypes[pluralType](count);\n}\n\nfunction createMemoizedPluralTypeNameSelector() {\n  var localePluralTypeStorage = {};\n\n  return function (pluralRules, locale) {\n    var pluralType = localePluralTypeStorage[locale];\n\n    if (pluralType && !pluralRules.pluralTypes[pluralType]) {\n      pluralType = null;\n      localePluralTypeStorage[locale] = pluralType;\n    }\n\n    if (!pluralType) {\n      pluralType = pluralTypeName(pluralRules, locale);\n\n      if (pluralType) {\n        localePluralTypeStorage[locale] = pluralType;\n      }\n    }\n\n    return pluralType;\n  };\n}\n\nfunction escape(token) {\n  return token.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nfunction constructTokenRegex(opts) {\n  var prefix = (opts && opts.prefix) || '%{';\n  var suffix = (opts && opts.suffix) || '}';\n\n  if (prefix === delimiter || suffix === delimiter) {\n    throw new RangeError('\"' + delimiter + '\" token is reserved for pluralization');\n  }\n\n  return new RegExp(escape(prefix) + '(.*?)' + escape(suffix), 'g');\n}\n\nvar memoizedPluralTypeName = createMemoizedPluralTypeNameSelector();\n\nvar defaultTokenRegex = /%\\{(.*?)\\}/g;\n\n// ### transformPhrase(phrase, substitutions, locale)\n//\n// Takes a phrase string and transforms it by choosing the correct\n// plural form and interpolating it.\n//\n//     transformPhrase('Hello, %{name}!', {name: 'Spike'});\n//     // \"Hello, Spike!\"\n//\n// The correct plural form is selected if substitutions.smart_count\n// is set. You can pass in a number instead of an Object as `substitutions`\n// as a shortcut for `smart_count`.\n//\n//     transformPhrase('%{smart_count} new messages |||| 1 new message', {smart_count: 1}, 'en');\n//     // \"1 new message\"\n//\n//     transformPhrase('%{smart_count} new messages |||| 1 new message', {smart_count: 2}, 'en');\n//     // \"2 new messages\"\n//\n//     transformPhrase('%{smart_count} new messages |||| 1 new message', 5, 'en');\n//     // \"5 new messages\"\n//\n// You should pass in a third argument, the locale, to specify the correct plural type.\n// It defaults to `'en'` with 2 plural forms.\nfunction transformPhrase(\n  phrase,\n  substitutions,\n  locale,\n  tokenRegex,\n  pluralRules,\n  replaceImplementation\n) {\n  if (typeof phrase !== 'string') {\n    throw new TypeError('Polyglot.transformPhrase expects argument #1 to be string');\n  }\n\n  if (substitutions == null) {\n    return phrase;\n  }\n\n  var result = phrase;\n  var interpolationRegex = tokenRegex || defaultTokenRegex;\n  var replace = replaceImplementation || defaultReplace;\n\n  // allow number as a pluralization shortcut\n  var options = typeof substitutions === 'number' ? { smart_count: substitutions } : substitutions;\n\n  // Select plural form: based on a phrase text that contains `n`\n  // plural forms separated by `delimiter`, a `locale`, and a `substitutions.smart_count`,\n  // choose the correct plural form. This is only done if `count` is set.\n  if (options.smart_count != null && phrase) {\n    var pluralRulesOrDefault = pluralRules || defaultPluralRules;\n    var texts = split.call(phrase, delimiter);\n    var bestLocale = locale || 'en';\n    var pluralType = memoizedPluralTypeName(pluralRulesOrDefault, bestLocale);\n    var pluralTypeWithCount = pluralTypeIndex(\n      pluralRulesOrDefault,\n      pluralType,\n      options.smart_count\n    );\n\n    result = defaultReplace.call(texts[pluralTypeWithCount] || texts[0], /^[^\\S]*|[^\\S]*$/g, '');\n  }\n\n  // Interpolate: Creates a `RegExp` object for each interpolation placeholder.\n  result = replace.call(result, interpolationRegex, function (expression, argument) {\n    if (!has(options, argument) || options[argument] == null) { return expression; }\n    return options[argument];\n  });\n\n  return result;\n}\n\n// ### Polyglot class constructor\nfunction Polyglot(options) {\n  var opts = options || {};\n  this.phrases = {};\n  this.extend(opts.phrases || {});\n  this.currentLocale = opts.locale || 'en';\n  var allowMissing = opts.allowMissing ? transformPhrase : null;\n  this.onMissingKey = typeof opts.onMissingKey === 'function' ? opts.onMissingKey : allowMissing;\n  this.warn = opts.warn || warn;\n  this.replaceImplementation = opts.replace || defaultReplace;\n  this.tokenRegex = constructTokenRegex(opts.interpolation);\n  this.pluralRules = opts.pluralRules || defaultPluralRules;\n}\n\n// ### polyglot.locale([locale])\n//\n// Get or set locale. Internally, Polyglot only uses locale for pluralization.\nPolyglot.prototype.locale = function (newLocale) {\n  if (newLocale) this.currentLocale = newLocale;\n  return this.currentLocale;\n};\n\n// ### polyglot.extend(phrases)\n//\n// Use `extend` to tell Polyglot how to translate a given key.\n//\n//     polyglot.extend({\n//       \"hello\": \"Hello\",\n//       \"hello_name\": \"Hello, %{name}\"\n//     });\n//\n// The key can be any string.  Feel free to call `extend` multiple times;\n// it will override any phrases with the same key, but leave existing phrases\n// untouched.\n//\n// It is also possible to pass nested phrase objects, which get flattened\n// into an object with the nested keys concatenated using dot notation.\n//\n//     polyglot.extend({\n//       \"nav\": {\n//         \"hello\": \"Hello\",\n//         \"hello_name\": \"Hello, %{name}\",\n//         \"sidebar\": {\n//           \"welcome\": \"Welcome\"\n//         }\n//       }\n//     });\n//\n//     console.log(polyglot.phrases);\n//     // {\n//     //   'nav.hello': 'Hello',\n//     //   'nav.hello_name': 'Hello, %{name}',\n//     //   'nav.sidebar.welcome': 'Welcome'\n//     // }\n//\n// `extend` accepts an optional second argument, `prefix`, which can be used\n// to prefix every key in the phrases object with some string, using dot\n// notation.\n//\n//     polyglot.extend({\n//       \"hello\": \"Hello\",\n//       \"hello_name\": \"Hello, %{name}\"\n//     }, \"nav\");\n//\n//     console.log(polyglot.phrases);\n//     // {\n//     //   'nav.hello': 'Hello',\n//     //   'nav.hello_name': 'Hello, %{name}'\n//     // }\n//\n// This feature is used internally to support nested phrase objects.\nPolyglot.prototype.extend = function (morePhrases, prefix) {\n  var phraseEntries = entries(morePhrases || {});\n  for (var i = 0; i < phraseEntries.length; i += 1) {\n    var key = phraseEntries[i][0];\n    var phrase = phraseEntries[i][1];\n    var prefixedKey = prefix ? prefix + '.' + key : key;\n    if (typeof phrase === 'object') {\n      this.extend(phrase, prefixedKey);\n    } else {\n      this.phrases[prefixedKey] = phrase;\n    }\n  }\n};\n\n// ### polyglot.unset(phrases)\n// Use `unset` to selectively remove keys from a polyglot instance.\n//\n//     polyglot.unset(\"some_key\");\n//     polyglot.unset({\n//       \"hello\": \"Hello\",\n//       \"hello_name\": \"Hello, %{name}\"\n//     });\n//\n// The unset method can take either a string (for the key), or an object hash with\n// the keys that you would like to unset.\nPolyglot.prototype.unset = function (morePhrases, prefix) {\n  if (typeof morePhrases === 'string') {\n    delete this.phrases[morePhrases];\n  } else {\n    var phraseEntries = entries(morePhrases || {});\n    for (var i = 0; i < phraseEntries.length; i += 1) {\n      var key = phraseEntries[i][0];\n      var phrase = phraseEntries[i][1];\n      var prefixedKey = prefix ? prefix + '.' + key : key;\n      if (typeof phrase === 'object') {\n        this.unset(phrase, prefixedKey);\n      } else {\n        delete this.phrases[prefixedKey];\n      }\n    }\n  }\n};\n\n// ### polyglot.clear()\n//\n// Clears all phrases. Useful for special cases, such as freeing\n// up memory if you have lots of phrases but no longer need to\n// perform any translation. Also used internally by `replace`.\nPolyglot.prototype.clear = function () {\n  this.phrases = {};\n};\n\n// ### polyglot.replace(phrases)\n//\n// Completely replace the existing phrases with a new set of phrases.\n// Normally, just use `extend` to add more phrases, but under certain\n// circumstances, you may want to make sure no old phrases are lying around.\nPolyglot.prototype.replace = function (newPhrases) {\n  this.clear();\n  this.extend(newPhrases);\n};\n\n// ### polyglot.t(key, options)\n//\n// The most-used method. Provide a key, and `t` will return the\n// phrase.\n//\n//     polyglot.t(\"hello\");\n//     => \"Hello\"\n//\n// The phrase value is provided first by a call to `polyglot.extend()` or\n// `polyglot.replace()`.\n//\n// Pass in an object as the second argument to perform interpolation.\n//\n//     polyglot.t(\"hello_name\", {name: \"Spike\"});\n//     => \"Hello, Spike\"\n//\n// If you like, you can provide a default value in case the phrase is missing.\n// Use the special option key \"_\" to specify a default.\n//\n//     polyglot.t(\"i_like_to_write_in_language\", {\n//       _: \"I like to write in %{language}.\",\n//       language: \"JavaScript\"\n//     });\n//     => \"I like to write in JavaScript.\"\n//\nPolyglot.prototype.t = function (key, options) {\n  var phrase, result;\n  var opts = options == null ? {} : options;\n  if (typeof this.phrases[key] === 'string') {\n    phrase = this.phrases[key];\n  } else if (typeof opts._ === 'string') {\n    phrase = opts._;\n  } else if (this.onMissingKey) {\n    var onMissingKey = this.onMissingKey;\n    result = onMissingKey(\n      key,\n      opts,\n      this.currentLocale,\n      this.tokenRegex,\n      this.pluralRules,\n      this.replaceImplementation\n    );\n  } else {\n    this.warn('Missing translation for key: \"' + key + '\"');\n    result = key;\n  }\n  if (typeof phrase === 'string') {\n    result = transformPhrase(\n      phrase,\n      opts,\n      this.currentLocale,\n      this.tokenRegex,\n      this.pluralRules,\n      this.replaceImplementation\n    );\n  }\n  return result;\n};\n\n// ### polyglot.has(key)\n//\n// Check if polyglot has a translation for given key\nPolyglot.prototype.has = function (key) {\n  return has(this.phrases, key);\n};\n\n// export transformPhrase\nPolyglot.transformPhrase = function transform(phrase, substitutions, locale) {\n  return transformPhrase(phrase, substitutions, locale);\n};\n\nmodule.exports = Polyglot;\n", "import Polyglot from 'node-polyglot';\n\nimport { I18nProvider, TranslationMessages, Locale } from 'ra-core';\n\ntype GetMessages = (\n    locale: string\n) => TranslationMessages | Promise<TranslationMessages>;\n\n/**\n * Build a polyglot-based i18nProvider based on a function returning the messages for a locale\n *\n * @example\n *\n * import { Admin, Resource } from 'react-admin';\n * import polyglotI18nProvider from 'ra-i18n-polyglot';\n * import englishMessages from 'ra-language-english';\n * import frenchMessages from 'ra-language-french';\n *\n * const messages = {\n *     fr: frenchMessages,\n *     en: englishMessages,\n * };\n * const i18nProvider = polyglotI18nProvider(\n *     locale => messages[locale],\n *     'en',\n *     [{ locale: 'en', name: 'English' }, { locale: 'fr', name: 'Français' }]\n * )\n */\nexport default (\n    getMessages: GetMessages,\n    initialLocale: string = 'en',\n    availableLocales: Locale[] | any = [{ locale: 'en', name: 'English' }],\n    polyglotOptions: any = {}\n): I18nProvider => {\n    let locale = initialLocale;\n    const messages = getMessages(initialLocale);\n    if (messages instanceof Promise) {\n        throw new Error(\n            `The i18nProvider returned a Promise for the messages of the default locale (${initialLocale}). Please update your i18nProvider to return the messages of the default locale in a synchronous way.`\n        );\n    }\n\n    let availableLocalesFinal, polyglotOptionsFinal;\n    if (Array.isArray(availableLocales)) {\n        // third argument is an array of locales\n        availableLocalesFinal = availableLocales;\n        polyglotOptionsFinal = polyglotOptions;\n    } else {\n        // third argument is the polyglotOptions\n        availableLocalesFinal = [{ locale: 'en', name: 'English' }];\n        polyglotOptionsFinal = availableLocales;\n    }\n    const polyglot = new Polyglot({\n        locale,\n        phrases: { '': '', ...messages },\n        ...polyglotOptionsFinal,\n    });\n    let translate = polyglot.t.bind(polyglot);\n\n    return {\n        translate: (key: string, options: any = {}) => translate(key, options),\n        changeLocale: (newLocale: string) =>\n            // We systematically return a Promise for the messages because\n            // getMessages may return a Promise\n            Promise.resolve(getMessages(newLocale as string)).then(\n                (messages: TranslationMessages) => {\n                    locale = newLocale;\n                    const newPolyglot = new Polyglot({\n                        locale: newLocale,\n                        phrases: { '': '', ...messages },\n                        ...polyglotOptions,\n                    });\n                    translate = newPolyglot.t.bind(newPolyglot);\n                }\n            ),\n        getLocale: () => locale,\n        getLocales: () => availableLocalesFinal,\n    };\n};\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAEA,QAAI,QAAQ,OAAO,UAAU;AAE7B,WAAO,UAAU,SAAS,YAAY,OAAO;AAC5C,UAAI,MAAM,MAAM,KAAK,KAAK;AAC1B,UAAI,SAAS,QAAQ;AACrB,UAAI,CAAC,QAAQ;AACZ,iBAAS,QAAQ,oBAChB,UAAU,QACV,OAAO,UAAU,YACjB,OAAO,MAAM,WAAW,YACxB,MAAM,UAAU,KAChB,MAAM,KAAK,MAAM,MAAM,MAAM;AAAA,MAC/B;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,QAAI;AACJ,QAAI,CAAC,OAAO,MAAM;AAEb,YAAM,OAAO,UAAU;AACvB,cAAQ,OAAO,UAAU;AACzB,eAAS;AACT,qBAAe,OAAO,UAAU;AAChC,uBAAiB,CAAC,aAAa,KAAK,EAAE,UAAU,KAAK,GAAG,UAAU;AAClE,wBAAkB,aAAa,KAAK,WAAY;AAAA,MAAC,GAAG,WAAW;AAC/D,kBAAY;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AACI,mCAA6B,SAAU,GAAG;AAC7C,YAAI,OAAO,EAAE;AACb,eAAO,QAAQ,KAAK,cAAc;AAAA,MACnC;AACI,qBAAe;AAAA,QAClB,mBAAmB;AAAA,QACnB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,SAAS;AAAA,QACT,cAAc;AAAA,QACd,aAAa;AAAA,QACb,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,cAAc;AAAA,QACd,cAAc;AAAA,QACd,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,UAAU;AAAA,QACV,OAAO;AAAA,QACP,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,SAAS;AAAA,MACV;AACI,iCAA4B,WAAY;AAE3C,YAAI,OAAO,WAAW,aAAa;AAAE,iBAAO;AAAA,QAAO;AACnD,iBAAS,KAAK,QAAQ;AACrB,cAAI;AACH,gBAAI,CAAC,aAAa,MAAM,CAAC,KAAK,IAAI,KAAK,QAAQ,CAAC,KAAK,OAAO,CAAC,MAAM,QAAQ,OAAO,OAAO,CAAC,MAAM,UAAU;AACzG,kBAAI;AACH,2CAA2B,OAAO,CAAC,CAAC;AAAA,cACrC,SAAS,GAAG;AACX,uBAAO;AAAA,cACR;AAAA,YACD;AAAA,UACD,SAAS,GAAG;AACX,mBAAO;AAAA,UACR;AAAA,QACD;AACA,eAAO;AAAA,MACR,EAAE;AACE,6CAAuC,SAAU,GAAG;AAEvD,YAAI,OAAO,WAAW,eAAe,CAAC,0BAA0B;AAC/D,iBAAO,2BAA2B,CAAC;AAAA,QACpC;AACA,YAAI;AACH,iBAAO,2BAA2B,CAAC;AAAA,QACpC,SAAS,GAAG;AACX,iBAAO;AAAA,QACR;AAAA,MACD;AAEA,iBAAW,SAAS,KAAK,QAAQ;AAChC,YAAI,WAAW,WAAW,QAAQ,OAAO,WAAW;AACpD,YAAI,aAAa,MAAM,KAAK,MAAM,MAAM;AACxC,YAAI,cAAc,OAAO,MAAM;AAC/B,YAAI,WAAW,YAAY,MAAM,KAAK,MAAM,MAAM;AAClD,YAAI,UAAU,CAAC;AAEf,YAAI,CAAC,YAAY,CAAC,cAAc,CAAC,aAAa;AAC7C,gBAAM,IAAI,UAAU,oCAAoC;AAAA,QACzD;AAEA,YAAI,YAAY,mBAAmB;AACnC,YAAI,YAAY,OAAO,SAAS,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,GAAG;AAC1D,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACvC,oBAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,UACvB;AAAA,QACD;AAEA,YAAI,eAAe,OAAO,SAAS,GAAG;AACrC,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACvC,oBAAQ,KAAK,OAAO,CAAC,CAAC;AAAA,UACvB;AAAA,QACD,OAAO;AACN,mBAAS,QAAQ,QAAQ;AACxB,gBAAI,EAAE,aAAa,SAAS,gBAAgB,IAAI,KAAK,QAAQ,IAAI,GAAG;AACnE,sBAAQ,KAAK,OAAO,IAAI,CAAC;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAEA,YAAI,gBAAgB;AACnB,cAAI,kBAAkB,qCAAqC,MAAM;AAEjE,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AAC1C,gBAAI,EAAE,mBAAmB,UAAU,CAAC,MAAM,kBAAkB,IAAI,KAAK,QAAQ,UAAU,CAAC,CAAC,GAAG;AAC3F,sBAAQ,KAAK,UAAU,CAAC,CAAC;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AACA,eAAO;AAAA,MACR;AAAA,IACD;AAnHK;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AAIA;AAyBA;AAkBA;AAsDL,WAAO,UAAU;AAAA;AAAA;;;ACzHjB;AAAA;AAAA;AAEA,QAAI,QAAQ,MAAM,UAAU;AAC5B,QAAI,SAAS;AAEb,QAAI,WAAW,OAAO;AACtB,QAAI,WAAW,WAAW,SAAS,KAAK,GAAG;AAAE,aAAO,SAAS,CAAC;AAAA,IAAG,IAAI;AAErE,QAAI,eAAe,OAAO;AAE1B,aAAS,OAAO,SAAS,iBAAiB;AACzC,UAAI,OAAO,MAAM;AAChB,YAAI,yBAA0B,WAAY;AAEzC,cAAI,OAAO,OAAO,KAAK,SAAS;AAChC,iBAAO,QAAQ,KAAK,WAAW,UAAU;AAAA,QAC1C,EAAE,GAAG,CAAC;AACN,YAAI,CAAC,wBAAwB;AAC5B,iBAAO,OAAO,SAAS,KAAK,QAAQ;AACnC,gBAAI,OAAO,MAAM,GAAG;AACnB,qBAAO,aAAa,MAAM,KAAK,MAAM,CAAC;AAAA,YACvC;AACA,mBAAO,aAAa,MAAM;AAAA,UAC3B;AAAA,QACD;AAAA,MACD,OAAO;AACN,eAAO,OAAO;AAAA,MACf;AACA,aAAO,OAAO,QAAQ;AAAA,IACvB;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/BjB;AAAA;AAAA;AAGA,QAAI,kBAAkB,OAAO,kBAAkB;AAC/C,QAAI,iBAAiB;AACpB,UAAI;AACH,wBAAgB,CAAC,GAAG,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,MACtC,SAAS,GAAG;AAEX,0BAAkB;AAAA,MACnB;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACbjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU,OAAO;AAAA;AAAA;;;ACHxB;AAAA;AAAA;AAGA,QAAI,QAAQ;AAEZ,QAAI,OAAO;AACV,UAAI;AACH,cAAM,CAAC,GAAG,QAAQ;AAAA,MACnB,SAAS,GAAG;AAEX,gBAAQ;AAAA,MACT;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACdjB;AAAA;AAAA;AAEA,QAAI,kBAAkB;AAEtB,QAAI,eAAe;AACnB,QAAI,aAAa;AAEjB,QAAI,OAAO;AAGX,WAAO,UAAU,SAAS,mBACzB,KACA,UACA,OACC;AACD,UAAI,CAAC,OAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAa;AACnE,cAAM,IAAI,WAAW,wCAAwC;AAAA,MAC9D;AACA,UAAI,OAAO,aAAa,YAAY,OAAO,aAAa,UAAU;AACjE,cAAM,IAAI,WAAW,0CAA0C;AAAA,MAChE;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,UAAU,CAAC,MAAM,aAAa,UAAU,CAAC,MAAM,MAAM;AACvF,cAAM,IAAI,WAAW,yDAAyD;AAAA,MAC/E;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,UAAU,CAAC,MAAM,aAAa,UAAU,CAAC,MAAM,MAAM;AACvF,cAAM,IAAI,WAAW,uDAAuD;AAAA,MAC7E;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,UAAU,CAAC,MAAM,aAAa,UAAU,CAAC,MAAM,MAAM;AACvF,cAAM,IAAI,WAAW,2DAA2D;AAAA,MACjF;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,UAAU,CAAC,MAAM,WAAW;AAC9D,cAAM,IAAI,WAAW,yCAAyC;AAAA,MAC/D;AAEA,UAAI,gBAAgB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAC1D,UAAI,cAAc,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACxD,UAAI,kBAAkB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAC5D,UAAI,QAAQ,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAGlD,UAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,KAAK,QAAQ;AAEvC,UAAI,iBAAiB;AACpB,wBAAgB,KAAK,UAAU;AAAA,UAC9B,cAAc,oBAAoB,QAAQ,OAAO,KAAK,eAAe,CAAC;AAAA,UACtE,YAAY,kBAAkB,QAAQ,OAAO,KAAK,aAAa,CAAC;AAAA,UAChE;AAAA,UACA,UAAU,gBAAgB,QAAQ,OAAO,KAAK,WAAW,CAAC;AAAA,QAC3D,CAAC;AAAA,MACF,WAAW,SAAU,CAAC,iBAAiB,CAAC,eAAe,CAAC,iBAAkB;AAEzE,YAAI,QAAQ,IAAI;AAAA,MACjB,OAAO;AACN,cAAM,IAAI,aAAa,6GAA6G;AAAA,MACrI;AAAA,IACD;AAAA;AAAA;;;ACvDA;AAAA;AAAA;AAEA,QAAI,kBAAkB;AAEtB,QAAI,yBAAyB,SAASA,0BAAyB;AAC9D,aAAO,CAAC,CAAC;AAAA,IACV;AAEA,2BAAuB,0BAA0B,SAAS,0BAA0B;AAEnF,UAAI,CAAC,iBAAiB;AACrB,eAAO;AAAA,MACR;AACA,UAAI;AACH,eAAO,gBAAgB,CAAC,GAAG,UAAU,EAAE,OAAO,EAAE,CAAC,EAAE,WAAW;AAAA,MAC/D,SAAS,GAAG;AAEX,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACrBjB;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,aAAa,OAAO,WAAW,cAAc,OAAO,OAAO,KAAK,MAAM;AAE1E,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,SAAS,MAAM,UAAU;AAC7B,QAAI,qBAAqB;AAEzB,QAAI,aAAa,SAAU,IAAI;AAC9B,aAAO,OAAO,OAAO,cAAc,MAAM,KAAK,EAAE,MAAM;AAAA,IACvD;AAEA,QAAI,sBAAsB,mCAAoC;AAE9D,QAAI,iBAAiB,SAAU,QAAQ,MAAM,OAAO,WAAW;AAC9D,UAAI,QAAQ,QAAQ;AACnB,YAAI,cAAc,MAAM;AACvB,cAAI,OAAO,IAAI,MAAM,OAAO;AAC3B;AAAA,UACD;AAAA,QACD,WAAW,CAAC,WAAW,SAAS,KAAK,CAAC,UAAU,GAAG;AAClD;AAAA,QACD;AAAA,MACD;AAEA,UAAI,qBAAqB;AACxB,2BAAmB,QAAQ,MAAM,OAAO,IAAI;AAAA,MAC7C,OAAO;AACN,2BAAmB,QAAQ,MAAM,KAAK;AAAA,MACvC;AAAA,IACD;AAEA,QAAI,mBAAmB,SAAU,QAAQ,KAAK;AAC7C,UAAI,aAAa,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC;AACxD,UAAI,QAAQ,KAAK,GAAG;AACpB,UAAI,YAAY;AACf,gBAAQ,OAAO,KAAK,OAAO,OAAO,sBAAsB,GAAG,CAAC;AAAA,MAC7D;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACzC,uBAAe,QAAQ,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC;AAAA,MACrE;AAAA,IACD;AAEA,qBAAiB,sBAAsB,CAAC,CAAC;AAEzC,WAAO,UAAU;AAAA;AAAA;;;AC9CjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,KAAK;AAAA;AAAA;;;ACHtB;AAAA;AAAA;AAGA,WAAO,UAAU,OAAO,SAAS,SAASC,OAAM,GAAG;AAClD,aAAO,MAAM;AAAA,IACd;AAAA;AAAA;;;ACLA;AAAA;AAAA;AAEA,QAAI,SAAS;AAGb,WAAO,UAAU,SAAS,KAAK,QAAQ;AACtC,UAAI,OAAO,MAAM,KAAK,WAAW,GAAG;AACnC,eAAO;AAAA,MACR;AACA,aAAO,SAAS,IAAI,KAAK;AAAA,IAC1B;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAIA,WAAO,UAAU,SAAS,aAAa;AACtC,UAAI,OAAO,WAAW,cAAc,OAAO,OAAO,0BAA0B,YAAY;AAAE,eAAO;AAAA,MAAO;AACxG,UAAI,OAAO,OAAO,aAAa,UAAU;AAAE,eAAO;AAAA,MAAM;AAGxD,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,OAAO,MAAM;AACvB,UAAI,SAAS,OAAO,GAAG;AACvB,UAAI,OAAO,QAAQ,UAAU;AAAE,eAAO;AAAA,MAAO;AAE7C,UAAI,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,mBAAmB;AAAE,eAAO;AAAA,MAAO;AAC/E,UAAI,OAAO,UAAU,SAAS,KAAK,MAAM,MAAM,mBAAmB;AAAE,eAAO;AAAA,MAAO;AAUlF,UAAI,SAAS;AACb,UAAI,GAAG,IAAI;AACX,eAAS,KAAK,KAAK;AAAE,eAAO;AAAA,MAAO;AACnC,UAAI,OAAO,OAAO,SAAS,cAAc,OAAO,KAAK,GAAG,EAAE,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAExF,UAAI,OAAO,OAAO,wBAAwB,cAAc,OAAO,oBAAoB,GAAG,EAAE,WAAW,GAAG;AAAE,eAAO;AAAA,MAAO;AAEtH,UAAI,OAAO,OAAO,sBAAsB,GAAG;AAC3C,UAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,KAAK;AAAE,eAAO;AAAA,MAAO;AAE1D,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,KAAK,GAAG,GAAG;AAAE,eAAO;AAAA,MAAO;AAE3E,UAAI,OAAO,OAAO,6BAA6B,YAAY;AAE1D,YAAI;AAAA;AAAA,UAAgD,OAAO,yBAAyB,KAAK,GAAG;AAAA;AAC5F,YAAI,WAAW,UAAU,UAAU,WAAW,eAAe,MAAM;AAAE,iBAAO;AAAA,QAAO;AAAA,MACpF;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC5CA;AAAA;AAAA;AAEA,QAAI,aAAa,OAAO,WAAW,eAAe;AAClD,QAAI,gBAAgB;AAGpB,WAAO,UAAU,SAAS,mBAAmB;AAC5C,UAAI,OAAO,eAAe,YAAY;AAAE,eAAO;AAAA,MAAO;AACtD,UAAI,OAAO,WAAW,YAAY;AAAE,eAAO;AAAA,MAAO;AAClD,UAAI,OAAO,WAAW,KAAK,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AAC3D,UAAI,OAAO,OAAO,KAAK,MAAM,UAAU;AAAE,eAAO;AAAA,MAAO;AAEvD,aAAO,cAAc;AAAA,IACtB;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAGA,WAAO,UAAW,OAAO,YAAY,eAAe,QAAQ,kBAAmB;AAAA;AAAA;;;ACH/E;AAAA;AAAA;AAEA,QAAI,UAAU;AAGd,WAAO,UAAU,QAAQ,kBAAkB;AAAA;AAAA;;;ACL3C,IAAAC,0BAAA;AAAA;AAAA;AAIA,QAAI,gBAAgB;AACpB,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,MAAM,KAAK;AACf,QAAI,WAAW;AAEf,QAAI,WAAW,SAASC,UAAS,GAAG,GAAG;AACnC,UAAI,MAAM,CAAC;AAEX,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AAClC,YAAI,CAAC,IAAI,EAAE,CAAC;AAAA,MAChB;AACA,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,GAAG;AAClC,YAAI,IAAI,EAAE,MAAM,IAAI,EAAE,CAAC;AAAA,MAC3B;AAEA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAASC,OAAM,SAAS,QAAQ;AACxC,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,UAAU,GAAG,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG,KAAK,GAAG;AACjE,YAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,MACtB;AACA,aAAO;AAAA,IACX;AAEA,QAAI,QAAQ,SAAU,KAAK,QAAQ;AAC/B,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,GAAG;AACpC,eAAO,IAAI,CAAC;AACZ,YAAI,IAAI,IAAI,IAAI,QAAQ;AACpB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAEA,WAAO,UAAU,SAAS,KAAK,MAAM;AACjC,UAAI,SAAS;AACb,UAAI,OAAO,WAAW,cAAc,MAAM,MAAM,MAAM,MAAM,UAAU;AAClE,cAAM,IAAI,UAAU,gBAAgB,MAAM;AAAA,MAC9C;AACA,UAAI,OAAO,MAAM,WAAW,CAAC;AAE7B,UAAI;AACJ,UAAI,SAAS,WAAY;AACrB,YAAI,gBAAgB,OAAO;AACvB,cAAI,SAAS,OAAO;AAAA,YAChB;AAAA,YACA,SAAS,MAAM,SAAS;AAAA,UAC5B;AACA,cAAI,OAAO,MAAM,MAAM,QAAQ;AAC3B,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,QACX;AACA,eAAO,OAAO;AAAA,UACV;AAAA,UACA,SAAS,MAAM,SAAS;AAAA,QAC5B;AAAA,MAEJ;AAEA,UAAI,cAAc,IAAI,GAAG,OAAO,SAAS,KAAK,MAAM;AACpD,UAAI,YAAY,CAAC;AACjB,eAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AAClC,kBAAU,CAAC,IAAI,MAAM;AAAA,MACzB;AAEA,cAAQ,SAAS,UAAU,sBAAsB,MAAM,WAAW,GAAG,IAAI,2CAA2C,EAAE,MAAM;AAE5H,UAAI,OAAO,WAAW;AAClB,YAAI,QAAQ,SAASC,SAAQ;AAAA,QAAC;AAC9B,cAAM,YAAY,OAAO;AACzB,cAAM,YAAY,IAAI,MAAM;AAC5B,cAAM,YAAY;AAAA,MACtB;AAEA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACnFA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AAErB,WAAO,UAAU,SAAS,UAAU,QAAQ;AAAA;AAAA;;;ACJ5C;AAAA;AAAA;AAGA,WAAO,UAAU,SAAS,UAAU;AAAA;AAAA;;;ACHpC;AAAA;AAAA;AAGA,WAAO,UAAU,SAAS,UAAU;AAAA;AAAA;;;ACHpC;AAAA;AAAA;AAGA,WAAO,UAAU,OAAO,YAAY,eAAe,WAAW,QAAQ;AAAA;AAAA;;;ACHtE;AAAA;AAAA;AAEA,QAAI,OAAO;AAEX,QAAI,SAAS;AACb,QAAI,QAAQ;AACZ,QAAI,gBAAgB;AAGpB,WAAO,UAAU,iBAAiB,KAAK,KAAK,OAAO,MAAM;AAAA;AAAA;;;ACTzD;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,aAAa;AAEjB,QAAI,QAAQ;AACZ,QAAI,eAAe;AAGnB,WAAO,UAAU,SAAS,cAAc,MAAM;AAC7C,UAAI,KAAK,SAAS,KAAK,OAAO,KAAK,CAAC,MAAM,YAAY;AACrD,cAAM,IAAI,WAAW,wBAAwB;AAAA,MAC9C;AACA,aAAO,aAAa,MAAM,OAAO,IAAI;AAAA,IACtC;AAAA;AAAA;;;ACdA;AAAA;AAAA;AAEA,QAAI,WAAW;AACf,QAAI,OAAO;AAEX,QAAI;AACJ,QAAI;AAEH;AAAA,MAA0E,CAAC,EAAG,cAAc,MAAM;AAAA,IACnG,SAAS,GAAG;AACX,UAAI,CAAC,KAAK,OAAO,MAAM,YAAY,EAAE,UAAU,MAAM,EAAE,SAAS,oBAAoB;AACnF,cAAM;AAAA,MACP;AAAA,IACD;AAGA,QAAI,OAAO,CAAC,CAAC,oBAAoB,QAAQ;AAAA,MAAK,OAAO;AAAA;AAAA,MAAyD;AAAA,IAAY;AAE1H,QAAI,UAAU;AACd,QAAI,kBAAkB,QAAQ;AAG9B,WAAO,UAAU,QAAQ,OAAO,KAAK,QAAQ,aAC1C,SAAS,CAAC,KAAK,GAAG,CAAC,IACnB,OAAO,oBAAoB;AAAA;AAAA,MACK,SAAS,UAAU,OAAO;AAE1D,eAAO,gBAAgB,SAAS,OAAO,QAAQ,QAAQ,KAAK,CAAC;AAAA,MAC9D;AAAA,QACE;AAAA;AAAA;;;AC7BJ;AAAA;AAAA;AAEA,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AAEvB,QAAI,iBAAiB;AAGrB,WAAO,UAAU,kBACd,SAAS,SAAS,GAAG;AAEtB,aAAO,gBAAgB,CAAC;AAAA,IACzB,IACE,mBACC,SAAS,SAAS,GAAG;AACtB,UAAI,CAAC,KAAM,OAAO,MAAM,YAAY,OAAO,MAAM,YAAa;AAC7D,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC9C;AAEA,aAAO,iBAAiB,CAAC;AAAA,IAC1B,IACE,iBACC,SAAS,SAAS,GAAG;AAEtB,aAAO,eAAe,CAAC;AAAA,IACxB,IACE;AAAA;AAAA;;;AC1BL;AAAA;AAAA;AAEA,QAAI,OAAO,SAAS,UAAU;AAC9B,QAAI,UAAU,OAAO,UAAU;AAC/B,QAAI,OAAO;AAGX,WAAO,UAAU,KAAK,KAAK,MAAM,OAAO;AAAA;AAAA;;;ACPxC;AAAA;AAAA;AAEA,QAAIC;AAEJ,QAAI,UAAU;AAEd,QAAI,SAAS;AACb,QAAI,aAAa;AACjB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,QAAI,YAAY;AAEhB,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,MAAM;AACV,QAAI,MAAM;AACV,QAAI,MAAM;AACV,QAAI,QAAQ;AACZ,QAAI,OAAO;AAEX,QAAI,YAAY;AAGhB,QAAI,wBAAwB,SAAU,kBAAkB;AACvD,UAAI;AACH,eAAO,UAAU,2BAA2B,mBAAmB,gBAAgB,EAAE;AAAA,MAClF,SAAS,GAAG;AAAA,MAAC;AAAA,IACd;AAEA,QAAI,QAAQ;AACZ,QAAI,kBAAkB;AAEtB,QAAI,iBAAiB,WAAY;AAChC,YAAM,IAAI,WAAW;AAAA,IACtB;AACA,QAAI,iBAAiB,QACjB,WAAY;AACd,UAAI;AAEH,kBAAU;AACV,eAAO;AAAA,MACR,SAAS,cAAc;AACtB,YAAI;AAEH,iBAAO,MAAM,WAAW,QAAQ,EAAE;AAAA,QACnC,SAAS,YAAY;AACpB,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD,EAAE,IACA;AAEH,QAAI,aAAa,sBAAuB;AAExC,QAAI,WAAW;AACf,QAAI,aAAa;AACjB,QAAI,cAAc;AAElB,QAAI,SAAS;AACb,QAAI,QAAQ;AAEZ,QAAI,YAAY,CAAC;AAEjB,QAAI,aAAa,OAAO,eAAe,eAAe,CAAC,WAAWA,aAAY,SAAS,UAAU;AAEjG,QAAI,aAAa;AAAA,MAChB,WAAW;AAAA,MACX,oBAAoB,OAAO,mBAAmB,cAAcA,aAAY;AAAA,MACxE,WAAW;AAAA,MACX,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,4BAA4B,cAAc,WAAW,SAAS,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,IAAIA;AAAA,MACvF,oCAAoCA;AAAA,MACpC,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,4BAA4B;AAAA,MAC5B,4BAA4B;AAAA,MAC5B,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,YAAY,OAAO,WAAW,cAAcA,aAAY;AAAA,MACxD,mBAAmB,OAAO,kBAAkB,cAAcA,aAAY;AAAA,MACtE,oBAAoB,OAAO,mBAAmB,cAAcA,aAAY;AAAA,MACxE,aAAa;AAAA,MACb,cAAc,OAAO,aAAa,cAAcA,aAAY;AAAA,MAC5D,UAAU;AAAA,MACV,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,eAAe;AAAA,MACf,wBAAwB;AAAA,MACxB,WAAW;AAAA,MACX,UAAU;AAAA;AAAA,MACV,eAAe;AAAA,MACf,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,kBAAkB,OAAO,iBAAiB,cAAcA,aAAY;AAAA,MACpE,0BAA0B,OAAO,yBAAyB,cAAcA,aAAY;AAAA,MACpF,cAAc;AAAA,MACd,uBAAuB;AAAA,MACvB,eAAe,OAAO,cAAc,cAAcA,aAAY;AAAA,MAC9D,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,cAAc;AAAA,MACd,WAAW;AAAA,MACX,uBAAuB,cAAc,WAAW,SAAS,SAAS,CAAC,EAAE,OAAO,QAAQ,EAAE,CAAC,CAAC,IAAIA;AAAA,MAC5F,UAAU,OAAO,SAAS,WAAW,OAAOA;AAAA,MAC5C,SAAS,OAAO,QAAQ,cAAcA,aAAY;AAAA,MAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAWA,aAAY,UAAS,oBAAI,IAAI,GAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,MACpI,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,qCAAqC;AAAA,MACrC,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,WAAW,OAAO,UAAU,cAAcA,aAAY;AAAA,MACtD,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,MACpB,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,YAAY;AAAA,MACZ,SAAS,OAAO,QAAQ,cAAcA,aAAY;AAAA,MAClD,0BAA0B,OAAO,QAAQ,eAAe,CAAC,cAAc,CAAC,WAAWA,aAAY,UAAS,oBAAI,IAAI,GAAE,OAAO,QAAQ,EAAE,CAAC;AAAA,MACpI,uBAAuB,OAAO,sBAAsB,cAAcA,aAAY;AAAA,MAC9E,YAAY;AAAA,MACZ,6BAA6B,cAAc,WAAW,SAAS,GAAG,OAAO,QAAQ,EAAE,CAAC,IAAIA;AAAA,MACxF,YAAY,aAAa,SAASA;AAAA,MAClC,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,gBAAgB,OAAO,eAAe,cAAcA,aAAY;AAAA,MAChE,uBAAuB,OAAO,sBAAsB,cAAcA,aAAY;AAAA,MAC9E,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,iBAAiB,OAAO,gBAAgB,cAAcA,aAAY;AAAA,MAClE,cAAc;AAAA,MACd,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAC1D,aAAa,OAAO,YAAY,cAAcA,aAAY;AAAA,MAE1D,6BAA6B;AAAA,MAC7B,8BAA8B;AAAA,MAC9B,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,4BAA4B;AAAA,IAC7B;AAEA,QAAI,UAAU;AACb,UAAI;AACH,aAAK;AAAA,MACN,SAAS,GAAG;AAEP,qBAAa,SAAS,SAAS,CAAC,CAAC;AACrC,mBAAW,mBAAmB,IAAI;AAAA,MACnC;AAAA,IACD;AAHM;AAKN,QAAI,SAAS,SAASC,QAAO,MAAM;AAClC,UAAI;AACJ,UAAI,SAAS,mBAAmB;AAC/B,gBAAQ,sBAAsB,sBAAsB;AAAA,MACrD,WAAW,SAAS,uBAAuB;AAC1C,gBAAQ,sBAAsB,iBAAiB;AAAA,MAChD,WAAW,SAAS,4BAA4B;AAC/C,gBAAQ,sBAAsB,uBAAuB;AAAA,MACtD,WAAW,SAAS,oBAAoB;AACvC,YAAI,KAAKA,QAAO,0BAA0B;AAC1C,YAAI,IAAI;AACP,kBAAQ,GAAG;AAAA,QACZ;AAAA,MACD,WAAW,SAAS,4BAA4B;AAC/C,YAAI,MAAMA,QAAO,kBAAkB;AACnC,YAAI,OAAO,UAAU;AACpB,kBAAQ,SAAS,IAAI,SAAS;AAAA,QAC/B;AAAA,MACD;AAEA,iBAAW,IAAI,IAAI;AAEnB,aAAO;AAAA,IACR;AAEA,QAAI,iBAAiB;AAAA,MACpB,WAAW;AAAA,MACX,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,oBAAoB,CAAC,SAAS,WAAW;AAAA,MACzC,wBAAwB,CAAC,SAAS,aAAa,SAAS;AAAA,MACxD,wBAAwB,CAAC,SAAS,aAAa,SAAS;AAAA,MACxD,qBAAqB,CAAC,SAAS,aAAa,MAAM;AAAA,MAClD,uBAAuB,CAAC,SAAS,aAAa,QAAQ;AAAA,MACtD,4BAA4B,CAAC,iBAAiB,WAAW;AAAA,MACzD,oBAAoB,CAAC,0BAA0B,WAAW;AAAA,MAC1D,6BAA6B,CAAC,0BAA0B,aAAa,WAAW;AAAA,MAChF,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,mBAAmB,CAAC,QAAQ,WAAW;AAAA,MACvC,oBAAoB,CAAC,SAAS,WAAW;AAAA,MACzC,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,2BAA2B,CAAC,gBAAgB,WAAW;AAAA,MACvD,2BAA2B,CAAC,gBAAgB,WAAW;AAAA,MACvD,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,eAAe,CAAC,qBAAqB,WAAW;AAAA,MAChD,wBAAwB,CAAC,qBAAqB,aAAa,WAAW;AAAA,MACtE,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,eAAe,CAAC,QAAQ,OAAO;AAAA,MAC/B,mBAAmB,CAAC,QAAQ,WAAW;AAAA,MACvC,kBAAkB,CAAC,OAAO,WAAW;AAAA,MACrC,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,uBAAuB,CAAC,UAAU,aAAa,UAAU;AAAA,MACzD,sBAAsB,CAAC,UAAU,aAAa,SAAS;AAAA,MACvD,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,uBAAuB,CAAC,WAAW,aAAa,MAAM;AAAA,MACtD,iBAAiB,CAAC,WAAW,KAAK;AAAA,MAClC,oBAAoB,CAAC,WAAW,QAAQ;AAAA,MACxC,qBAAqB,CAAC,WAAW,SAAS;AAAA,MAC1C,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,6BAA6B,CAAC,kBAAkB,WAAW;AAAA,MAC3D,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,kBAAkB,CAAC,OAAO,WAAW;AAAA,MACrC,gCAAgC,CAAC,qBAAqB,WAAW;AAAA,MACjE,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,qBAAqB,CAAC,UAAU,WAAW;AAAA,MAC3C,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,wBAAwB,CAAC,aAAa,WAAW;AAAA,MACjD,yBAAyB,CAAC,cAAc,WAAW;AAAA,MACnD,gCAAgC,CAAC,qBAAqB,WAAW;AAAA,MACjE,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,0BAA0B,CAAC,eAAe,WAAW;AAAA,MACrD,uBAAuB,CAAC,YAAY,WAAW;AAAA,MAC/C,sBAAsB,CAAC,WAAW,WAAW;AAAA,MAC7C,sBAAsB,CAAC,WAAW,WAAW;AAAA,IAC9C;AAEA,QAAI,OAAO;AACX,QAAI,SAAS;AACb,QAAI,UAAU,KAAK,KAAK,OAAO,MAAM,UAAU,MAAM;AACrD,QAAI,eAAe,KAAK,KAAK,QAAQ,MAAM,UAAU,MAAM;AAC3D,QAAI,WAAW,KAAK,KAAK,OAAO,OAAO,UAAU,OAAO;AACxD,QAAI,YAAY,KAAK,KAAK,OAAO,OAAO,UAAU,KAAK;AACvD,QAAI,QAAQ,KAAK,KAAK,OAAO,OAAO,UAAU,IAAI;AAGlD,QAAI,aAAa;AACjB,QAAI,eAAe;AACnB,QAAI,eAAe,SAASC,cAAa,QAAQ;AAChD,UAAI,QAAQ,UAAU,QAAQ,GAAG,CAAC;AAClC,UAAI,OAAO,UAAU,QAAQ,EAAE;AAC/B,UAAI,UAAU,OAAO,SAAS,KAAK;AAClC,cAAM,IAAI,aAAa,gDAAgD;AAAA,MACxE,WAAW,SAAS,OAAO,UAAU,KAAK;AACzC,cAAM,IAAI,aAAa,gDAAgD;AAAA,MACxE;AACA,UAAI,SAAS,CAAC;AACd,eAAS,QAAQ,YAAY,SAAU,OAAO,QAAQ,OAAO,WAAW;AACvE,eAAO,OAAO,MAAM,IAAI,QAAQ,SAAS,WAAW,cAAc,IAAI,IAAI,UAAU;AAAA,MACrF,CAAC;AACD,aAAO;AAAA,IACR;AAGA,QAAI,mBAAmB,SAASC,kBAAiB,MAAM,cAAc;AACpE,UAAI,gBAAgB;AACpB,UAAI;AACJ,UAAI,OAAO,gBAAgB,aAAa,GAAG;AAC1C,gBAAQ,eAAe,aAAa;AACpC,wBAAgB,MAAM,MAAM,CAAC,IAAI;AAAA,MAClC;AAEA,UAAI,OAAO,YAAY,aAAa,GAAG;AACtC,YAAI,QAAQ,WAAW,aAAa;AACpC,YAAI,UAAU,WAAW;AACxB,kBAAQ,OAAO,aAAa;AAAA,QAC7B;AACA,YAAI,OAAO,UAAU,eAAe,CAAC,cAAc;AAClD,gBAAM,IAAI,WAAW,eAAe,OAAO,sDAAsD;AAAA,QAClG;AAEA,eAAO;AAAA,UACN;AAAA,UACA,MAAM;AAAA,UACN;AAAA,QACD;AAAA,MACD;AAEA,YAAM,IAAI,aAAa,eAAe,OAAO,kBAAkB;AAAA,IAChE;AAEA,WAAO,UAAU,SAAS,aAAa,MAAM,cAAc;AAC1D,UAAI,OAAO,SAAS,YAAY,KAAK,WAAW,GAAG;AAClD,cAAM,IAAI,WAAW,2CAA2C;AAAA,MACjE;AACA,UAAI,UAAU,SAAS,KAAK,OAAO,iBAAiB,WAAW;AAC9D,cAAM,IAAI,WAAW,2CAA2C;AAAA,MACjE;AAEA,UAAI,MAAM,eAAe,IAAI,MAAM,MAAM;AACxC,cAAM,IAAI,aAAa,oFAAoF;AAAA,MAC5G;AACA,UAAI,QAAQ,aAAa,IAAI;AAC7B,UAAI,oBAAoB,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI;AAEtD,UAAI,YAAY,iBAAiB,MAAM,oBAAoB,KAAK,YAAY;AAC5E,UAAI,oBAAoB,UAAU;AAClC,UAAI,QAAQ,UAAU;AACtB,UAAI,qBAAqB;AAEzB,UAAI,QAAQ,UAAU;AACtB,UAAI,OAAO;AACV,4BAAoB,MAAM,CAAC;AAC3B,qBAAa,OAAO,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AAAA,MAC3C;AAEA,eAAS,IAAI,GAAG,QAAQ,MAAM,IAAI,MAAM,QAAQ,KAAK,GAAG;AACvD,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,QAAQ,UAAU,MAAM,GAAG,CAAC;AAChC,YAAI,OAAO,UAAU,MAAM,EAAE;AAC7B,aAEG,UAAU,OAAO,UAAU,OAAO,UAAU,QACzC,SAAS,OAAO,SAAS,OAAO,SAAS,SAE3C,UAAU,MACZ;AACD,gBAAM,IAAI,aAAa,sDAAsD;AAAA,QAC9E;AACA,YAAI,SAAS,iBAAiB,CAAC,OAAO;AACrC,+BAAqB;AAAA,QACtB;AAEA,6BAAqB,MAAM;AAC3B,4BAAoB,MAAM,oBAAoB;AAE9C,YAAI,OAAO,YAAY,iBAAiB,GAAG;AAC1C,kBAAQ,WAAW,iBAAiB;AAAA,QACrC,WAAW,SAAS,MAAM;AACzB,cAAI,EAAE,QAAQ,QAAQ;AACrB,gBAAI,CAAC,cAAc;AAClB,oBAAM,IAAI,WAAW,wBAAwB,OAAO,6CAA6C;AAAA,YAClG;AACA,mBAAO,KAAKH;AAAA,UACb;AACA,cAAI,SAAU,IAAI,KAAM,MAAM,QAAQ;AACrC,gBAAI,OAAO,MAAM,OAAO,IAAI;AAC5B,oBAAQ,CAAC,CAAC;AASV,gBAAI,SAAS,SAAS,QAAQ,EAAE,mBAAmB,KAAK,MAAM;AAC7D,sBAAQ,KAAK;AAAA,YACd,OAAO;AACN,sBAAQ,MAAM,IAAI;AAAA,YACnB;AAAA,UACD,OAAO;AACN,oBAAQ,OAAO,OAAO,IAAI;AAC1B,oBAAQ,MAAM,IAAI;AAAA,UACnB;AAEA,cAAI,SAAS,CAAC,oBAAoB;AACjC,uBAAW,iBAAiB,IAAI;AAAA,UACjC;AAAA,QACD;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzXA;AAAA;AAAA;AAEA,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,iBAAiB,mCAAoC;AACzD,QAAI,OAAO;AAEX,QAAI,aAAa;AACjB,QAAI,SAAS,aAAa,cAAc;AAGxC,WAAO,UAAU,SAAS,kBAAkB,IAAI,QAAQ;AACvD,UAAI,OAAO,OAAO,YAAY;AAC7B,cAAM,IAAI,WAAW,wBAAwB;AAAA,MAC9C;AACA,UAAI,OAAO,WAAW,YAAY,SAAS,KAAK,SAAS,cAAc,OAAO,MAAM,MAAM,QAAQ;AACjG,cAAM,IAAI,WAAW,4CAA4C;AAAA,MAClE;AAEA,UAAI,QAAQ,UAAU,SAAS,KAAK,CAAC,CAAC,UAAU,CAAC;AAEjD,UAAI,+BAA+B;AACnC,UAAI,2BAA2B;AAC/B,UAAI,YAAY,MAAM,MAAM;AAC3B,YAAI,OAAO,KAAK,IAAI,QAAQ;AAC5B,YAAI,QAAQ,CAAC,KAAK,cAAc;AAC/B,yCAA+B;AAAA,QAChC;AACA,YAAI,QAAQ,CAAC,KAAK,UAAU;AAC3B,qCAA2B;AAAA,QAC5B;AAAA,MACD;AAEA,UAAI,gCAAgC,4BAA4B,CAAC,OAAO;AACvE,YAAI,gBAAgB;AACnB;AAAA;AAAA,YAA6C;AAAA,YAAK;AAAA,YAAU;AAAA,YAAQ;AAAA,YAAM;AAAA,UAAI;AAAA,QAC/E,OAAO;AACN;AAAA;AAAA,YAA6C;AAAA,YAAK;AAAA,YAAU;AAAA,UAAM;AAAA,QACnE;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzCA;AAAA;AAAA;AAEA,QAAI,OAAO;AACX,QAAI,SAAS;AACb,QAAI,cAAc;AAGlB,WAAO,UAAU,SAAS,YAAY;AACrC,aAAO,YAAY,MAAM,QAAQ,SAAS;AAAA,IAC3C;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,QAAI,oBAAoB;AAExB,QAAI,kBAAkB;AAEtB,QAAI,gBAAgB;AACpB,QAAI,YAAY;AAEhB,WAAO,UAAU,SAAS,SAAS,kBAAkB;AACpD,UAAI,OAAO,cAAc,SAAS;AAClC,UAAI,iBAAiB,iBAAiB,UAAU,UAAU,SAAS;AACnE,aAAO;AAAA,QACN;AAAA,QACA,KAAK,iBAAiB,IAAI,iBAAiB;AAAA,QAC3C;AAAA,MACD;AAAA,IACD;AAEA,QAAI,iBAAiB;AACpB,sBAAgB,OAAO,SAAS,SAAS,EAAE,OAAO,UAAU,CAAC;AAAA,IAC9D,OAAO;AACN,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAAA;AAAA;;;ACvBA;AAAA;AAAA;AAEA,QAAI,aAAa;AAGjB,WAAO,UAAU,SAAS,uBAAuB,OAAO;AACvD,UAAI,SAAS,MAAM;AAClB,cAAM,IAAI,WAAY,UAAU,SAAS,KAAK,UAAU,CAAC,KAAO,2BAA2B,KAAM;AAAA,MAClG;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,QAAI,eAAe;AAEnB,QAAI,gBAAgB;AAGpB,QAAI,WAAW,cAAc,CAAC,aAAa,4BAA4B,CAAC,CAAC;AAGzE,WAAO,UAAU,SAAS,mBAAmB,MAAM,cAAc;AAGhE,UAAI;AAAA;AAAA,QAA2E,aAAa,MAAM,CAAC,CAAC,YAAY;AAAA;AAChH,UAAI,OAAO,cAAc,cAAc,SAAS,MAAM,aAAa,IAAI,IAAI;AAC1E,eAAO;AAAA;AAAA,UAAoC,CAAC,SAAS;AAAA,QAAE;AAAA,MACxD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;AClBA,IAAAI,0BAAA;AAAA;AAAA;AAEA,QAAI,yBAAyB;AAC7B,QAAI,YAAY;AAChB,QAAI,gBAAgB,UAAU,uCAAuC;AACrE,QAAI,QAAQ,UAAU,sBAAsB;AAE5C,WAAO,UAAU,SAAS,QAAQ,GAAG;AACpC,UAAI,MAAM,uBAAuB,CAAC;AAClC,UAAI,SAAS,CAAC;AACd,eAAS,OAAO,KAAK;AACpB,YAAI,cAAc,KAAK,GAAG,GAAG;AAC5B,gBAAM,QAAQ,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC;AAAA,QAC9B;AAAA,MACD;AACA,aAAO;AAAA,IACR;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,QAAI,iBAAiB;AAErB,WAAO,UAAU,SAAS,cAAc;AACvC,aAAO,OAAO,OAAO,YAAY,aAAa,OAAO,UAAU;AAAA,IAChE;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAEA,QAAI,cAAc;AAClB,QAAI,SAAS;AAEb,WAAO,UAAU,SAAS,cAAc;AACvC,UAAI,WAAW,YAAY;AAC3B,aAAO,QAAQ,EAAE,SAAS,SAAS,GAAG;AAAA,QACrC,SAAS,SAAS,cAAc;AAC/B,iBAAO,OAAO,YAAY;AAAA,QAC3B;AAAA,MACD,CAAC;AACD,aAAO;AAAA,IACR;AAAA;AAAA;;;ACbA;AAAA;AAAA;AAEA,QAAI,SAAS;AACb,QAAI,WAAW;AAEf,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,OAAO;AAEX,QAAI,WAAW,SAAS,YAAY,GAAG,MAAM;AAE7C,WAAO,UAAU;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAED,WAAO,UAAU;AAAA;AAAA;;;ACjBjB;AAAA;AAAA;AAgBA,QAAI,UAAU;AAEd,QAAI,UAAU,WAAW;AAAA,IAAC;AAE1B,QAAI,SAAS;AACP,qBAAe,SAASC,cAAa,QAAQ,MAAM;AACrD,YAAI,MAAM,UAAU;AACpB,eAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,iBAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAClC,eAAK,MAAM,CAAC,IAAI,UAAU,GAAG;AAAA,QAC/B;AACA,YAAI,WAAW;AACf,YAAI,UAAU,cACZ,OAAO,QAAQ,OAAO,WAAW;AAC/B,iBAAO,KAAK,UAAU;AAAA,QACxB,CAAC;AACH,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAEA,gBAAU,SAAS,WAAW,QAAQ,MAAM;AAC1C,YAAI,MAAM,UAAU;AACpB,eAAO,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,CAAC;AACtC,iBAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAClC,eAAK,MAAM,CAAC,IAAI,UAAU,GAAG;AAAA,QAC/B;AACA,YAAI,WAAW,QAAW;AACxB,gBAAM,IAAI;AAAA,YACN;AAAA,UAEJ;AAAA,QACF;AACA,YAAI,CAAC,WAAW;AACd,uBAAa,MAAM,MAAM,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAtCM;AAwCN,WAAO,UAAU;AAAA;AAAA;;;AC7DjB;AAAA;AAAA;AAmBA,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,MAAM;AAEV,QAAI,OAAO,SAASC,MAAK,SAAS;AAChC,cAAQ,OAAO,OAAO;AAAA,IACxB;AAEA,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,QAAQ,OAAO,UAAU;AAI7B,QAAI,YAAY;AAEhB,QAAI,sBAAsB,SAAU,GAAG;AACrC,UAAI,UAAU,IAAI;AAClB,UAAI,MAAM,UAAU;AACpB,UAAI,YAAY,MAAM,QAAQ,GAAG;AAC/B,eAAO;AAAA,MACT;AACA,UAAI,KAAK,OAAO,OAAO,KAAK,EAAE,WAAW,MAAM,WAAW,KAAK;AAC7D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAEA,QAAI,qBAAqB;AAAA;AAAA,MAEvB,aAAa;AAAA,QACX,QAAQ,SAAU,GAAG;AAEnB,cAAI,IAAI,GAAG;AAAE,mBAAO;AAAA,UAAG;AACvB,cAAI,UAAU,IAAI;AAClB,cAAI,WAAW,KAAK,WAAW,GAAI,QAAO;AAC1C,iBAAO,WAAW,KAAK,IAAI;AAAA,QAC7B;AAAA,QACA,iBAAiB;AAAA,QACjB,SAAS,WAAY;AAAE,iBAAO;AAAA,QAAG;AAAA,QACjC,UAAU;AAAA,QACV,QAAQ,SAAU,GAAG;AAAE,iBAAO,KAAK,IAAI,IAAI;AAAA,QAAG;AAAA,QAC9C,QAAQ,SAAU,GAAG;AAAE,iBAAO,MAAM,IAAI,IAAI;AAAA,QAAG;AAAA,QAC/C,SAAS;AAAA,QACT,YAAY,SAAU,GAAG;AACvB,cAAI,IAAI,OAAO,KAAK,IAAI,QAAQ,IAAI;AAAE,mBAAO;AAAA,UAAG;AAChD,iBAAO,IAAI,MAAM,KAAK,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;AAAA,QAC5E;AAAA,QACA,OAAO,SAAU,GAAG;AAClB,cAAI,MAAM,GAAG;AAAE,mBAAO;AAAA,UAAG;AACzB,iBAAQ,KAAK,KAAK,KAAK,IAAK,IAAI;AAAA,QAClC;AAAA,QACA,QAAQ,SAAU,GAAG;AACnB,cAAI,MAAM,GAAG;AAAE,mBAAO;AAAA,UAAG;AACzB,cAAI,MAAM,IAAI;AACd,iBAAO,KAAK,OAAO,OAAO,MAAM,IAAI,MAAM,MAAM,IAAI,OAAO,MAAM,IAAI;AAAA,QACvE;AAAA,QACA,WAAW,SAAU,GAAG;AAAE,iBAAQ,IAAI,OAAO,KAAK,IAAI,QAAQ,KAAM,IAAI;AAAA,QAAG;AAAA,QAC3E,WAAW,SAAU,GAAG;AACtB,cAAI,UAAU,IAAI;AAClB,cAAI,YAAY,GAAG;AACjB,mBAAO;AAAA,UACT;AACA,cAAI,YAAY,GAAG;AACjB,mBAAO;AAAA,UACT;AACA,cAAI,YAAY,KAAK,YAAY,GAAG;AAClC,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,UAAU,SAAU,GAAG;AACrB,cAAI,MAAM,GAAG;AAAE,mBAAO;AAAA,UAAG;AACzB,cAAI,UAAU,IAAI;AAClB,cAAI,MAAM,KAAM,WAAW,KAAK,WAAW,IAAK;AAAE,mBAAO;AAAA,UAAG;AAC5D,iBAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,MACb;AAAA;AAAA;AAAA;AAAA,MAKA,uBAAuB;AAAA,QACrB,QAAQ,CAAC,IAAI;AAAA,QACb,iBAAiB,CAAC,cAAc,cAAc,UAAU,OAAO;AAAA,QAC/D,SAAS,CAAC,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,MAAM,MAAM,SAAS,IAAI;AAAA,QAC7E,UAAU,CAAC,MAAM,OAAO;AAAA,QACxB,QAAQ,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,SAAS,MAAM,SAAS,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAAA,QACnH,QAAQ,CAAC,MAAM,MAAM,OAAO;AAAA,QAC5B,SAAS,CAAC,MAAM,OAAO;AAAA,QACvB,YAAY,CAAC,IAAI;AAAA,QACjB,OAAO,CAAC,MAAM,SAAS,IAAI;AAAA,QAC3B,QAAQ,CAAC,IAAI;AAAA,QACb,WAAW,CAAC,MAAM,IAAI;AAAA,QACtB,WAAW,CAAC,OAAO;AAAA,QACnB,UAAU,CAAC,IAAI;AAAA,QACf,WAAW,CAAC,MAAM,IAAI;AAAA,MACxB;AAAA,IACF;AAEA,aAAS,cAAc,SAAS;AAC9B,UAAI,MAAM,CAAC;AACX,UAAI,iBAAiB,QAAQ,OAAO;AACpC,eAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK,GAAG;AACjD,YAAI,OAAO,eAAe,CAAC,EAAE,CAAC;AAC9B,YAAI,QAAQ,eAAe,CAAC,EAAE,CAAC;AAC/B,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAI,MAAM,CAAC,CAAC,IAAI;AAAA,QAClB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,aAAS,eAAe,aAAa,QAAQ;AAC3C,UAAI,mBAAmB,cAAc,YAAY,qBAAqB;AACtE,aAAO,iBAAiB,MAAM,KACzB,iBAAiB,MAAM,KAAK,QAAQ,KAAK,CAAC,EAAE,CAAC,CAAC,KAC9C,iBAAiB;AAAA,IACxB;AAEA,aAAS,gBAAgB,aAAa,YAAY,OAAO;AACvD,aAAO,YAAY,YAAY,UAAU,EAAE,KAAK;AAAA,IAClD;AAEA,aAAS,uCAAuC;AAC9C,UAAI,0BAA0B,CAAC;AAE/B,aAAO,SAAU,aAAa,QAAQ;AACpC,YAAI,aAAa,wBAAwB,MAAM;AAE/C,YAAI,cAAc,CAAC,YAAY,YAAY,UAAU,GAAG;AACtD,uBAAa;AACb,kCAAwB,MAAM,IAAI;AAAA,QACpC;AAEA,YAAI,CAAC,YAAY;AACf,uBAAa,eAAe,aAAa,MAAM;AAE/C,cAAI,YAAY;AACd,oCAAwB,MAAM,IAAI;AAAA,UACpC;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,aAAS,OAAO,OAAO;AACrB,aAAO,MAAM,QAAQ,uBAAuB,MAAM;AAAA,IACpD;AAEA,aAAS,oBAAoB,MAAM;AACjC,UAAI,SAAU,QAAQ,KAAK,UAAW;AACtC,UAAI,SAAU,QAAQ,KAAK,UAAW;AAEtC,UAAI,WAAW,aAAa,WAAW,WAAW;AAChD,cAAM,IAAI,WAAW,MAAM,YAAY,uCAAuC;AAAA,MAChF;AAEA,aAAO,IAAI,OAAO,OAAO,MAAM,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;AAAA,IAClE;AAEA,QAAI,yBAAyB,qCAAqC;AAElE,QAAI,oBAAoB;AAyBxB,aAAS,gBACP,QACA,eACA,QACA,YACA,aACA,uBACA;AACA,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM,IAAI,UAAU,2DAA2D;AAAA,MACjF;AAEA,UAAI,iBAAiB,MAAM;AACzB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AACb,UAAI,qBAAqB,cAAc;AACvC,UAAI,UAAU,yBAAyB;AAGvC,UAAI,UAAU,OAAO,kBAAkB,WAAW,EAAE,aAAa,cAAc,IAAI;AAKnF,UAAI,QAAQ,eAAe,QAAQ,QAAQ;AACzC,YAAI,uBAAuB,eAAe;AAC1C,YAAI,QAAQ,MAAM,KAAK,QAAQ,SAAS;AACxC,YAAI,aAAa,UAAU;AAC3B,YAAI,aAAa,uBAAuB,sBAAsB,UAAU;AACxE,YAAI,sBAAsB;AAAA,UACxB;AAAA,UACA;AAAA,UACA,QAAQ;AAAA,QACV;AAEA,iBAAS,eAAe,KAAK,MAAM,mBAAmB,KAAK,MAAM,CAAC,GAAG,oBAAoB,EAAE;AAAA,MAC7F;AAGA,eAAS,QAAQ,KAAK,QAAQ,oBAAoB,SAAU,YAAY,UAAU;AAChF,YAAI,CAAC,IAAI,SAAS,QAAQ,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAAE,iBAAO;AAAA,QAAY;AAC/E,eAAO,QAAQ,QAAQ;AAAA,MACzB,CAAC;AAED,aAAO;AAAA,IACT;AAGA,aAASC,UAAS,SAAS;AACzB,UAAI,OAAO,WAAW,CAAC;AACvB,WAAK,UAAU,CAAC;AAChB,WAAK,OAAO,KAAK,WAAW,CAAC,CAAC;AAC9B,WAAK,gBAAgB,KAAK,UAAU;AACpC,UAAI,eAAe,KAAK,eAAe,kBAAkB;AACzD,WAAK,eAAe,OAAO,KAAK,iBAAiB,aAAa,KAAK,eAAe;AAClF,WAAK,OAAO,KAAK,QAAQ;AACzB,WAAK,wBAAwB,KAAK,WAAW;AAC7C,WAAK,aAAa,oBAAoB,KAAK,aAAa;AACxD,WAAK,cAAc,KAAK,eAAe;AAAA,IACzC;AAKA,IAAAA,UAAS,UAAU,SAAS,SAAU,WAAW;AAC/C,UAAI,UAAW,MAAK,gBAAgB;AACpC,aAAO,KAAK;AAAA,IACd;AAmDA,IAAAA,UAAS,UAAU,SAAS,SAAU,aAAa,QAAQ;AACzD,UAAI,gBAAgB,QAAQ,eAAe,CAAC,CAAC;AAC7C,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,GAAG;AAChD,YAAI,MAAM,cAAc,CAAC,EAAE,CAAC;AAC5B,YAAI,SAAS,cAAc,CAAC,EAAE,CAAC;AAC/B,YAAI,cAAc,SAAS,SAAS,MAAM,MAAM;AAChD,YAAI,OAAO,WAAW,UAAU;AAC9B,eAAK,OAAO,QAAQ,WAAW;AAAA,QACjC,OAAO;AACL,eAAK,QAAQ,WAAW,IAAI;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAaA,IAAAA,UAAS,UAAU,QAAQ,SAAU,aAAa,QAAQ;AACxD,UAAI,OAAO,gBAAgB,UAAU;AACnC,eAAO,KAAK,QAAQ,WAAW;AAAA,MACjC,OAAO;AACL,YAAI,gBAAgB,QAAQ,eAAe,CAAC,CAAC;AAC7C,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK,GAAG;AAChD,cAAI,MAAM,cAAc,CAAC,EAAE,CAAC;AAC5B,cAAI,SAAS,cAAc,CAAC,EAAE,CAAC;AAC/B,cAAI,cAAc,SAAS,SAAS,MAAM,MAAM;AAChD,cAAI,OAAO,WAAW,UAAU;AAC9B,iBAAK,MAAM,QAAQ,WAAW;AAAA,UAChC,OAAO;AACL,mBAAO,KAAK,QAAQ,WAAW;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,IAAAA,UAAS,UAAU,QAAQ,WAAY;AACrC,WAAK,UAAU,CAAC;AAAA,IAClB;AAOA,IAAAA,UAAS,UAAU,UAAU,SAAU,YAAY;AACjD,WAAK,MAAM;AACX,WAAK,OAAO,UAAU;AAAA,IACxB;AA2BA,IAAAA,UAAS,UAAU,IAAI,SAAU,KAAK,SAAS;AAC7C,UAAI,QAAQ;AACZ,UAAI,OAAO,WAAW,OAAO,CAAC,IAAI;AAClC,UAAI,OAAO,KAAK,QAAQ,GAAG,MAAM,UAAU;AACzC,iBAAS,KAAK,QAAQ,GAAG;AAAA,MAC3B,WAAW,OAAO,KAAK,MAAM,UAAU;AACrC,iBAAS,KAAK;AAAA,MAChB,WAAW,KAAK,cAAc;AAC5B,YAAI,eAAe,KAAK;AACxB,iBAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF,OAAO;AACL,aAAK,KAAK,mCAAmC,MAAM,GAAG;AACtD,iBAAS;AAAA,MACX;AACA,UAAI,OAAO,WAAW,UAAU;AAC9B,iBAAS;AAAA,UACP;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,IAAAA,UAAS,UAAU,MAAM,SAAU,KAAK;AACtC,aAAO,IAAI,KAAK,SAAS,GAAG;AAAA,IAC9B;AAGA,IAAAA,UAAS,kBAAkB,SAAS,UAAU,QAAQ,eAAe,QAAQ;AAC3E,aAAO,gBAAgB,QAAQ,eAAe,MAAM;AAAA,IACtD;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AC7cjB,2BAAqB;;;;;;;;;;;;AA4BrB,IAAA,cAAe,SACX,aACA,eACA,kBACA,iBAAyB;AAFzB,MAAA,kBAAA,QAAA;AAAA,oBAAA;EAA4B;AAC5B,MAAA,qBAAA,QAAA;AAAA,uBAAA,CAAoC,EAAE,QAAQ,MAAM,MAAM,UAAS,CAAE;EAAC;AACtE,MAAA,oBAAA,QAAA;AAAA,sBAAA,CAAA;EAAyB;AAEzB,MAAI,SAAS;AACb,MAAM,WAAW,YAAY,aAAa;AAC1C,MAAI,oBAAoB,SAAS;AAC7B,UAAM,IAAI,MACN,+EAAA,OAA+E,eAAa,uGAAA,CAAuG;;AAI3M,MAAI,uBAAuB;AAC3B,MAAI,MAAM,QAAQ,gBAAgB,GAAG;AAEjC,4BAAwB;AACxB,2BAAuB;SACpB;AAEH,4BAAwB,CAAC,EAAE,QAAQ,MAAM,MAAM,UAAS,CAAE;AAC1D,2BAAuB;;AAE3B,MAAM,WAAW,IAAI,qBAAAC,QAAQ,SAAA,EACzB,QACA,SAAO,SAAA,EAAI,IAAI,GAAE,GAAK,QAAQ,EAAA,GAC3B,oBAAoB,CAAA;AAE3B,MAAI,YAAY,SAAS,EAAE,KAAK,QAAQ;AAExC,SAAO;IACH,WAAW,SAAC,KAAa,SAAiB;AAAjB,UAAA,YAAA,QAAA;AAAA,kBAAA,CAAA;MAAiB;AAAK,aAAA,UAAU,KAAK,OAAO;IAAtB;IAC/C,cAAc,SAAC,WAAiB;AAG5B,aAAA,QAAQ,QAAQ,YAAY,SAAmB,CAAC,EAAE,KAC9C,SAACC,WAA6B;AAC1B,iBAAS;AACT,YAAM,cAAc,IAAI,qBAAAD,QAAQ,SAAA,EAC5B,QAAQ,WACR,SAAO,SAAA,EAAI,IAAI,GAAE,GAAKC,SAAQ,EAAA,GAC3B,eAAe,CAAA;AAEtB,oBAAY,YAAY,EAAE,KAAK,WAAW;MAC9C,CAAC;IATL;IAWJ,WAAW,WAAA;AAAM,aAAA;IAAA;IACjB,YAAY,WAAA;AAAM,aAAA;IAAA;;AAE1B;", "names": ["hasPropertyDescriptors", "isNaN", "require_implementation", "concatty", "slicy", "Empty", "undefined", "<PERSON><PERSON><PERSON>", "stringToPath", "getBaseIntrinsic", "require_implementation", "printWarning", "warn", "Polyglot", "Polyglot", "messages"]}