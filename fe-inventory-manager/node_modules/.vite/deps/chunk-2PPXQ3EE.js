import {
  createSvgIcon
} from "./chunk-VQEH6ZLL.js";
import {
  require_jsx_runtime
} from "./chunk-NAXKE64U.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/@mui/icons-material/esm/FormatBold.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var FormatBold_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M15.6 10.79c.97-.67 1.65-1.77 1.65-2.79 0-2.26-1.75-4-4-4H7v14h7.04c2.09 0 3.71-1.7 3.71-3.79 0-1.52-.86-2.82-2.15-3.42M10 6.5h3c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5h-3zm3.5 9H10v-3h3.5c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5"
}), "FormatBold");

// node_modules/@mui/icons-material/esm/FormatItalic.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var FormatItalic_default = createSvgIcon((0, import_jsx_runtime2.jsx)("path", {
  d: "M10 4v3h2.21l-3.42 8H6v3h8v-3h-2.21l3.42-8H18V4z"
}), "FormatItalic");

// node_modules/@mui/icons-material/esm/FormatUnderlined.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var FormatUnderlined_default = createSvgIcon((0, import_jsx_runtime3.jsx)("path", {
  d: "M12 17c3.31 0 6-2.69 6-6V3h-2.5v8c0 1.93-1.57 3.5-3.5 3.5S8.5 12.93 8.5 11V3H6v8c0 3.31 2.69 6 6 6m-7 2v2h14v-2z"
}), "FormatUnderlined");

// node_modules/@mui/icons-material/esm/FormatStrikethrough.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var FormatStrikethrough_default = createSvgIcon((0, import_jsx_runtime4.jsx)("path", {
  d: "M10 19h4v-3h-4zM5 4v3h5v3h4V7h5V4zM3 14h18v-2H3z"
}), "FormatStrikethrough");

// node_modules/@mui/icons-material/esm/FormatListBulleted.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var FormatListBulleted_default = createSvgIcon((0, import_jsx_runtime5.jsx)("path", {
  d: "M4 10.5c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5m0-6c-.83 0-1.5.67-1.5 1.5S3.17 7.5 4 7.5 5.5 6.83 5.5 6 4.83 4.5 4 4.5m0 12c-.83 0-1.5.68-1.5 1.5s.68 1.5 1.5 1.5 1.5-.68 1.5-1.5-.67-1.5-1.5-1.5M7 19h14v-2H7zm0-6h14v-2H7zm0-8v2h14V5z"
}), "FormatListBulleted");

// node_modules/@mui/icons-material/esm/FormatListNumbered.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var FormatListNumbered_default = createSvgIcon((0, import_jsx_runtime6.jsx)("path", {
  d: "M2 17h2v.5H3v1h1v.5H2v1h3v-4H2zm1-9h1V4H2v1h1zm-1 3h1.8L2 13.1v.9h3v-1H3.2L5 10.9V10H2zm5-6v2h14V5zm0 14h14v-2H7zm0-6h14v-2H7z"
}), "FormatListNumbered");

// node_modules/@mui/icons-material/esm/FormatAlignCenter.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var FormatAlignCenter_default = createSvgIcon((0, import_jsx_runtime7.jsx)("path", {
  d: "M7 15v2h10v-2zm-4 6h18v-2H3zm0-8h18v-2H3zm4-6v2h10V7zM3 3v2h18V3z"
}), "FormatAlignCenter");

// node_modules/@mui/icons-material/esm/FormatAlignLeft.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var FormatAlignLeft_default = createSvgIcon((0, import_jsx_runtime8.jsx)("path", {
  d: "M15 15H3v2h12zm0-8H3v2h12zM3 13h18v-2H3zm0 8h18v-2H3zM3 3v2h18V3z"
}), "FormatAlignLeft");

// node_modules/@mui/icons-material/esm/FormatAlignRight.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var FormatAlignRight_default = createSvgIcon((0, import_jsx_runtime9.jsx)("path", {
  d: "M3 21h18v-2H3zm6-4h12v-2H9zm-6-4h18v-2H3zm6-4h12V7H9zM3 3v2h18V3z"
}), "FormatAlignRight");

// node_modules/@mui/icons-material/esm/FormatAlignJustify.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var FormatAlignJustify_default = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M3 21h18v-2H3zm0-4h18v-2H3zm0-4h18v-2H3zm0-4h18V7H3zm0-6v2h18V3z"
}), "FormatAlignJustify");

// node_modules/@mui/icons-material/esm/InsertLink.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var InsertLink_default = createSvgIcon((0, import_jsx_runtime11.jsx)("path", {
  d: "M3.9 12c0-1.71 1.39-3.1 3.1-3.1h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-1.9H7c-1.71 0-3.1-1.39-3.1-3.1M8 13h8v-2H8zm9-6h-4v1.9h4c1.71 0 3.1 1.39 3.1 3.1s-1.39 3.1-3.1 3.1h-4V17h4c2.76 0 5-2.24 5-5s-2.24-5-5-5"
}), "InsertLink");

// node_modules/@mui/icons-material/esm/FormatQuote.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var FormatQuote_default = createSvgIcon((0, import_jsx_runtime12.jsx)("path", {
  d: "M6 17h3l2-4V7H5v6h3zm8 0h3l2-4V7h-6v6h3z"
}), "FormatQuote");

// node_modules/@mui/icons-material/esm/FormatClear.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var FormatClear_default = createSvgIcon((0, import_jsx_runtime13.jsx)("path", {
  d: "M3.27 5 2 6.27l6.97 6.97L6.5 19h3l1.57-3.66L16.73 21 18 19.73 3.55 5.27zM6 5v.18L8.82 8h2.4l-.72 1.68 2.1 2.1L14.21 8H20V5z"
}), "FormatClear");

// node_modules/@mui/icons-material/esm/Image.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var Image_default = createSvgIcon((0, import_jsx_runtime14.jsx)("path", {
  d: "M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z"
}), "Image");

// node_modules/@mui/icons-material/esm/FormatColorText.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var FormatColorText_default = createSvgIcon((0, import_jsx_runtime15.jsx)("path", {
  d: "M2 20h20v4H2zm3.49-3h2.42l1.27-3.58h5.65L16.09 17h2.42L13.25 3h-2.5zm4.42-5.61 2.03-5.79h.12l2.03 5.79z"
}), "FormatColorText");

// node_modules/@mui/icons-material/esm/FontDownload.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var FontDownload_default = createSvgIcon((0, import_jsx_runtime16.jsx)("path", {
  d: "M9.93 13.5h4.14L12 7.98zM20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m-4.05 16.5-1.14-3H9.17l-1.12 3H5.96l5.11-13h1.86l5.11 13z"
}), "FontDownload");

export {
  FormatBold_default,
  FormatItalic_default,
  FormatUnderlined_default,
  FormatStrikethrough_default,
  FormatListBulleted_default,
  FormatListNumbered_default,
  FormatAlignCenter_default,
  FormatAlignLeft_default,
  FormatAlignRight_default,
  FormatAlignJustify_default,
  InsertLink_default,
  FormatQuote_default,
  FormatClear_default,
  Image_default,
  FormatColorText_default,
  FontDownload_default
};
//# sourceMappingURL=chunk-2PPXQ3EE.js.map
