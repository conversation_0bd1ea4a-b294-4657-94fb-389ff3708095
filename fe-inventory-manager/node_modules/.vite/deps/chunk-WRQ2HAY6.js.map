{"version": 3, "sources": ["../../@mui/icons-material/esm/ManageAccounts.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"10\",\n  cy: \"8\",\n  r: \"4\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M10.67 13.02c-.22-.01-.44-.02-.67-.02-2.42 0-4.68.67-6.61 1.82-.88.52-1.39 1.5-1.39 2.53V20h9.26c-.79-1.13-1.26-2.51-1.26-4 0-1.07.25-2.07.67-2.98M20.75 16c0-.22-.03-.42-.06-.63l1.14-1.01-1-1.73-1.45.49q-.48-.405-1.08-.63L18 11h-2l-.3 1.49q-.6.225-1.08.63l-1.45-.49-1 1.73 1.14 1.01c-.03.21-.06.41-.06.63s.03.42.06.63l-1.14 1.01 1 1.73 1.45-.49q.48.405 1.08.63L16 21h2l.3-1.49q.6-.225 1.08-.63l1.45.49 1-1.73-1.14-1.01c.03-.21.06-.41.06-.63M17 18c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2\"\n}, \"1\")], 'ManageAccounts');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,yBAAQ,cAAc,KAAc,mBAAAA,KAAK,UAAU;AAAA,EACxD,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG;AACL,GAAG,GAAG,OAAgB,mBAAAA,KAAK,QAAQ;AAAA,EACjC,GAAG;AACL,GAAG,GAAG,CAAC,GAAG,gBAAgB;", "names": ["_jsx"]}