import {
  createSvgIcon
} from "./chunk-HDAR62HQ.js";
import {
  require_jsx_runtime
} from "./chunk-NAXKE64U.js";
import {
  __toESM
} from "./chunk-SNAQBZPT.js";

// node_modules/@mui/icons-material/esm/AccountCircle.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var AccountCircle_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 4c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6m0 14c-2.03 0-4.43-.82-6.14-2.88C7.55 15.8 9.68 15 12 15s4.45.8 6.14 2.12C16.43 19.18 14.03 20 12 20"
}), "AccountCircle");

// node_modules/@mui/icons-material/esm/Add.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var Add_default = createSvgIcon((0, import_jsx_runtime2.jsx)("path", {
  d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"
}), "Add");

// node_modules/@mui/icons-material/esm/AddCircleOutline.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var AddCircleOutline_default = createSvgIcon((0, import_jsx_runtime3.jsx)("path", {
  d: "M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"
}), "AddCircleOutline");

// node_modules/@mui/icons-material/esm/ArrowCircleDown.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime());
var ArrowCircleDown_default = createSvgIcon((0, import_jsx_runtime4.jsx)("path", {
  d: "M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 10V8h-2v4H8l4 4 4-4z"
}), "ArrowCircleDown");

// node_modules/@mui/icons-material/esm/ArrowCircleUp.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime());
var ArrowCircleUp_default = createSvgIcon((0, import_jsx_runtime5.jsx)("path", {
  d: "M12 20c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0 2c5.52 0 10-4.48 10-10S17.52 2 12 2 2 6.48 2 12s4.48 10 10 10m-1-10v4h2v-4h3l-4-4-4 4z"
}), "ArrowCircleUp");

// node_modules/@mui/icons-material/esm/ArrowDropDown.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime());
var ArrowDropDown_default = createSvgIcon((0, import_jsx_runtime6.jsx)("path", {
  d: "m7 10 5 5 5-5z"
}), "ArrowDropDown");

// node_modules/@mui/icons-material/esm/BookmarkAdd.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime());
var BookmarkAdd_default = createSvgIcon((0, import_jsx_runtime7.jsx)("path", {
  d: "M21 7h-2v2h-2V7h-2V5h2V3h2v2h2zm-2 14-7-3-7 3V5c0-1.1.9-2 2-2h7c-.63.84-1 1.87-1 3 0 2.76 2.24 5 5 5 .34 0 .68-.03 1-.1z"
}), "BookmarkAdd");

// node_modules/@mui/icons-material/esm/BookmarkBorder.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime());
var BookmarkBorder_default = createSvgIcon((0, import_jsx_runtime8.jsx)("path", {
  d: "M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2m0 15-5-2.18L7 18V5h10z"
}), "BookmarkBorder");

// node_modules/@mui/icons-material/esm/BookmarkRemove.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime());
var BookmarkRemove_default = createSvgIcon((0, import_jsx_runtime9.jsx)("path", {
  d: "M21 7h-6V5h6zm-2 3.9c-.32.07-.66.1-1 .1-2.76 0-5-2.24-5-5 0-1.13.37-2.16 1-3H7c-1.1 0-2 .9-2 2v16l7-3 7 3z"
}), "BookmarkRemove");

// node_modules/@mui/icons-material/esm/Brightness4.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime());
var Brightness4_default = createSvgIcon((0, import_jsx_runtime10.jsx)("path", {
  d: "M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6"
}), "Brightness4");

// node_modules/@mui/icons-material/esm/Brightness7.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime());
var Brightness7_default = createSvgIcon((0, import_jsx_runtime11.jsx)("path", {
  d: "M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6m0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4"
}), "Brightness7");

// node_modules/@mui/icons-material/esm/CancelOutlined.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime());
var CancelOutlined_default = createSvgIcon((0, import_jsx_runtime12.jsx)("path", {
  d: "M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m3.59-13L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41z"
}), "CancelOutlined");

// node_modules/@mui/icons-material/esm/CheckBox.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime());
var CheckBox_default = createSvgIcon((0, import_jsx_runtime13.jsx)("path", {
  d: "M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"
}), "CheckBox");

// node_modules/@mui/icons-material/esm/CheckBoxOutlineBlank.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime());
var CheckBoxOutlineBlank_default = createSvgIcon((0, import_jsx_runtime14.jsx)("path", {
  d: "M19 5v14H5V5zm0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2"
}), "CheckBoxOutlineBlank");

// node_modules/@mui/icons-material/esm/CheckCircle.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime());
var CheckCircle_default = createSvgIcon((0, import_jsx_runtime15.jsx)("path", {
  d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"
}), "CheckCircle");

// node_modules/@mui/icons-material/esm/Clear.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime());
var Clear_default = createSvgIcon((0, import_jsx_runtime16.jsx)("path", {
  d: "M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
}), "Clear");

// node_modules/@mui/icons-material/esm/Create.js
var import_jsx_runtime17 = __toESM(require_jsx_runtime());
var Create_default = createSvgIcon((0, import_jsx_runtime17.jsx)("path", {
  d: "M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.996.996 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"
}), "Create");

// node_modules/@mui/icons-material/esm/Dashboard.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime());
var Dashboard_default = createSvgIcon((0, import_jsx_runtime18.jsx)("path", {
  d: "M3 13h8V3H3zm0 8h8v-6H3zm10 0h8V11h-8zm0-18v6h8V3z"
}), "Dashboard");

// node_modules/@mui/icons-material/esm/Delete.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime());
var Delete_default = createSvgIcon((0, import_jsx_runtime19.jsx)("path", {
  d: "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM19 4h-3.5l-1-1h-5l-1 1H5v2h14z"
}), "Delete");

// node_modules/@mui/icons-material/esm/DeleteOutline.js
var import_jsx_runtime20 = __toESM(require_jsx_runtime());
var DeleteOutline_default = createSvgIcon((0, import_jsx_runtime20.jsx)("path", {
  d: "M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6zM8 9h8v10H8zm7.5-5-1-1h-5l-1 1H5v2h14V4z"
}), "DeleteOutline");

// node_modules/@mui/icons-material/esm/Done.js
var import_jsx_runtime21 = __toESM(require_jsx_runtime());
var Done_default = createSvgIcon((0, import_jsx_runtime21.jsx)("path", {
  d: "M9 16.2 4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4z"
}), "Done");

// node_modules/@mui/icons-material/esm/DragIndicator.js
var import_jsx_runtime22 = __toESM(require_jsx_runtime());
var DragIndicator_default = createSvgIcon((0, import_jsx_runtime22.jsx)("path", {
  d: "M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2m-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2m0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2m0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2"
}), "DragIndicator");

// node_modules/@mui/icons-material/esm/Error.js
var import_jsx_runtime23 = __toESM(require_jsx_runtime());
var Error_default = createSvgIcon((0, import_jsx_runtime23.jsx)("path", {
  d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m1 15h-2v-2h2zm0-4h-2V7h2z"
}), "Error");

// node_modules/@mui/icons-material/esm/ErrorOutline.js
var import_jsx_runtime24 = __toESM(require_jsx_runtime());
var ErrorOutline_default = createSvgIcon((0, import_jsx_runtime24.jsx)("path", {
  d: "M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2M12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"
}), "ErrorOutline");

// node_modules/@mui/icons-material/esm/FilterList.js
var import_jsx_runtime25 = __toESM(require_jsx_runtime());
var FilterList_default = createSvgIcon((0, import_jsx_runtime25.jsx)("path", {
  d: "M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z"
}), "FilterList");

// node_modules/@mui/icons-material/esm/GetApp.js
var import_jsx_runtime26 = __toESM(require_jsx_runtime());
var GetApp_default = createSvgIcon((0, import_jsx_runtime26.jsx)("path", {
  d: "M19 9h-4V3H9v6H5l7 7zM5 18v2h14v-2z"
}), "GetApp");

// node_modules/@mui/icons-material/esm/HelpOutline.js
var import_jsx_runtime27 = __toESM(require_jsx_runtime());
var HelpOutline_default = createSvgIcon((0, import_jsx_runtime27.jsx)("path", {
  d: "M11 18h2v-2h-2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"
}), "HelpOutline");

// node_modules/@mui/icons-material/esm/HighlightOff.js
var import_jsx_runtime28 = __toESM(require_jsx_runtime());
var HighlightOff_default = createSvgIcon((0, import_jsx_runtime28.jsx)("path", {
  d: "M14.59 8 12 10.59 9.41 8 8 9.41 10.59 12 8 14.59 9.41 16 12 13.41 14.59 16 16 14.59 13.41 12 16 9.41zM12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"
}), "HighlightOff");

// node_modules/@mui/icons-material/esm/History.js
var import_jsx_runtime29 = __toESM(require_jsx_runtime());
var History_default = createSvgIcon((0, import_jsx_runtime29.jsx)("path", {
  d: "M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9m-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8z"
}), "History");

// node_modules/@mui/icons-material/esm/HotTub.js
var import_jsx_runtime30 = __toESM(require_jsx_runtime());
var HotTub_default = createSvgIcon([(0, import_jsx_runtime30.jsx)("circle", {
  cx: "7",
  cy: "6",
  r: "2"
}, "0"), (0, import_jsx_runtime30.jsx)("path", {
  d: "M11.15 12c-.31-.22-.59-.46-.82-.72l-1.4-1.55c-.19-.21-.43-.38-.69-.5-.29-.14-.62-.23-.96-.23h-.03C6.01 9 5 10.01 5 11.25V12H2v8c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2v-8zM7 20H5v-6h2zm4 0H9v-6h2zm4 0h-2v-6h2zm4 0h-2v-6h2zm-.35-14.14-.07-.07c-.57-.62-.82-1.41-.67-2.2L18 3h-1.89l-.06.43c-.2 1.36.27 2.71 1.3 3.72l.07.06c.57.62.82 1.41.67 2.2l-.11.59h1.91l.06-.43c.21-1.36-.27-2.71-1.3-3.71m-4 0-.07-.07c-.57-.62-.82-1.41-.67-2.2L14 3h-1.89l-.06.43c-.2 1.36.27 2.71 1.3 3.72l.07.06c.57.62.82 1.41.67 2.2l-.11.59h1.91l.06-.43c.21-1.36-.27-2.71-1.3-3.71"
}, "1")], "HotTub");

// node_modules/@mui/icons-material/esm/Inbox.js
var import_jsx_runtime31 = __toESM(require_jsx_runtime());
var Inbox_default = createSvgIcon((0, import_jsx_runtime31.jsx)("path", {
  d: "M19 3H4.99c-1.11 0-1.98.89-1.98 2L3 19c0 1.1.88 2 1.99 2H19c1.1 0 2-.9 2-2V5c0-1.11-.9-2-2-2m0 12h-4c0 1.66-1.35 3-3 3s-3-1.34-3-3H4.99V5H19z"
}), "Inbox");

// node_modules/@mui/icons-material/esm/List.js
var import_jsx_runtime32 = __toESM(require_jsx_runtime());
var List_default = createSvgIcon((0, import_jsx_runtime32.jsx)("path", {
  d: "M3 13h2v-2H3zm0 4h2v-2H3zm0-8h2V7H3zm4 4h14v-2H7zm0 4h14v-2H7zM7 7v2h14V7z"
}), "List");

// node_modules/@mui/icons-material/esm/Lock.js
var import_jsx_runtime33 = __toESM(require_jsx_runtime());
var Lock_default = createSvgIcon((0, import_jsx_runtime33.jsx)("path", {
  d: "M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2m-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2m3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1s3.1 1.39 3.1 3.1z"
}), "Lock");

// node_modules/@mui/icons-material/esm/Menu.js
var import_jsx_runtime34 = __toESM(require_jsx_runtime());
var Menu_default = createSvgIcon((0, import_jsx_runtime34.jsx)("path", {
  d: "M3 18h18v-2H3zm0-5h18v-2H3zm0-7v2h18V6z"
}), "Menu");

// node_modules/@mui/icons-material/esm/NavigateBefore.js
var import_jsx_runtime35 = __toESM(require_jsx_runtime());
var NavigateBefore_default = createSvgIcon((0, import_jsx_runtime35.jsx)("path", {
  d: "M15.41 7.41 14 6l-6 6 6 6 1.41-1.41L10.83 12z"
}), "NavigateBefore");

// node_modules/@mui/icons-material/esm/NavigateNext.js
var import_jsx_runtime36 = __toESM(require_jsx_runtime());
var NavigateNext_default = createSvgIcon((0, import_jsx_runtime36.jsx)("path", {
  d: "M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"
}), "NavigateNext");

// node_modules/@mui/icons-material/esm/PowerSettingsNew.js
var import_jsx_runtime37 = __toESM(require_jsx_runtime());
var PowerSettingsNew_default = createSvgIcon((0, import_jsx_runtime37.jsx)("path", {
  d: "M13 3h-2v10h2zm4.83 2.17-1.42 1.42C17.99 7.86 19 9.81 19 12c0 3.87-3.13 7-7 7s-7-3.13-7-7c0-2.19 1.01-4.14 2.58-5.42L6.17 5.17C4.23 6.82 3 9.26 3 12c0 4.97 4.03 9 9 9s9-4.03 9-9c0-2.74-1.23-5.18-3.17-6.83"
}), "PowerSettingsNew");

// node_modules/@mui/icons-material/esm/Queue.js
var import_jsx_runtime38 = __toESM(require_jsx_runtime());
var Queue_default = createSvgIcon((0, import_jsx_runtime38.jsx)("path", {
  d: "M4 6H2v14c0 1.1.9 2 2 2h14v-2H4zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m-1 9h-4v4h-2v-4H9V9h4V5h2v4h4z"
}), "Queue");

// node_modules/@mui/icons-material/esm/Refresh.js
var import_jsx_runtime39 = __toESM(require_jsx_runtime());
var Refresh_default = createSvgIcon((0, import_jsx_runtime39.jsx)("path", {
  d: "M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"
}), "Refresh");

// node_modules/@mui/icons-material/esm/RemoveCircle.js
var import_jsx_runtime40 = __toESM(require_jsx_runtime(), 1);
var RemoveCircle_default = createSvgIcon((0, import_jsx_runtime40.jsx)("path", {
  d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m5 11H7v-2h10z"
}), "RemoveCircle");

// node_modules/@mui/icons-material/esm/RemoveCircleOutline.js
var import_jsx_runtime41 = __toESM(require_jsx_runtime());
var RemoveCircleOutline_default = createSvgIcon((0, import_jsx_runtime41.jsx)("path", {
  d: "M7 11v2h10v-2zm5-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8"
}), "RemoveCircleOutline");

// node_modules/@mui/icons-material/esm/RemoveRedEye.js
var import_jsx_runtime42 = __toESM(require_jsx_runtime());
var RemoveRedEye_default = createSvgIcon((0, import_jsx_runtime42.jsx)("path", {
  d: "M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"
}), "RemoveRedEye");

// node_modules/@mui/icons-material/esm/Report.js
var import_jsx_runtime43 = __toESM(require_jsx_runtime());
var Report_default = createSvgIcon((0, import_jsx_runtime43.jsx)("path", {
  d: "M15.73 3H8.27L3 8.27v7.46L8.27 21h7.46L21 15.73V8.27zM12 17.3c-.72 0-1.3-.58-1.3-1.3s.58-1.3 1.3-1.3 1.3.58 1.3 1.3-.58 1.3-1.3 1.3m1-4.3h-2V7h2z"
}), "Report");

// node_modules/@mui/icons-material/esm/Save.js
var import_jsx_runtime44 = __toESM(require_jsx_runtime());
var Save_default = createSvgIcon((0, import_jsx_runtime44.jsx)("path", {
  d: "M17 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V7zm-5 16c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m3-10H5V5h10z"
}), "Save");

// node_modules/@mui/icons-material/esm/Search.js
var import_jsx_runtime45 = __toESM(require_jsx_runtime());
var Search_default = createSvgIcon((0, import_jsx_runtime45.jsx)("path", {
  d: "M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"
}), "Search");

// node_modules/@mui/icons-material/esm/Settings.js
var import_jsx_runtime46 = __toESM(require_jsx_runtime());
var Settings_default = createSvgIcon((0, import_jsx_runtime46.jsx)("path", {
  d: "M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"
}), "Settings");

// node_modules/@mui/icons-material/esm/Sort.js
var import_jsx_runtime47 = __toESM(require_jsx_runtime());
var Sort_default = createSvgIcon((0, import_jsx_runtime47.jsx)("path", {
  d: "M3 18h6v-2H3zM3 6v2h18V6zm0 7h12v-2H3z"
}), "Sort");

// node_modules/@mui/icons-material/esm/Translate.js
var import_jsx_runtime48 = __toESM(require_jsx_runtime());
var Translate_default = createSvgIcon((0, import_jsx_runtime48.jsx)("path", {
  d: "m12.87 15.07-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2zm-2.62 7 1.62-4.33L19.12 17z"
}), "Translate");

// node_modules/@mui/icons-material/esm/Update.js
var import_jsx_runtime49 = __toESM(require_jsx_runtime());
var Update_default = createSvgIcon((0, import_jsx_runtime49.jsx)("path", {
  d: "M21 10.12h-6.78l2.74-2.82c-2.73-2.7-7.15-2.8-9.88-.1-2.73 2.71-2.73 7.08 0 9.79s7.15 2.71 9.88 0C18.32 15.65 19 14.08 19 12.1h2c0 1.98-.88 4.55-2.64 6.29-3.51 3.48-9.21 3.48-12.72 0-3.5-3.47-3.53-9.11-.02-12.58s9.14-3.47 12.65 0L21 3zM12.5 8v4.25l3.5 2.08-.72 1.21L11 13V8z"
}), "Update");

// node_modules/@mui/icons-material/esm/ViewList.js
var import_jsx_runtime50 = __toESM(require_jsx_runtime());
var ViewList_default = createSvgIcon((0, import_jsx_runtime50.jsx)("path", {
  d: "M3 14h4v-4H3zm0 5h4v-4H3zM3 9h4V5H3zm5 5h13v-4H8zm0 5h13v-4H8zM8 5v4h13V5z"
}), "ViewList");

// node_modules/@mui/icons-material/esm/ViewWeek.js
var import_jsx_runtime51 = __toESM(require_jsx_runtime());
var ViewWeek_default = createSvgIcon((0, import_jsx_runtime51.jsx)("path", {
  d: "M5.33 20H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2h1.33c1.1 0 2 .9 2 2v12c0 1.1-.89 2-2 2M22 18V6c0-1.1-.9-2-2-2h-1.33c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2H20c1.11 0 2-.9 2-2m-7.33 0V6c0-1.1-.9-2-2-2h-1.33c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h1.33c1.1 0 2-.9 2-2"
}), "ViewWeek");

// node_modules/@mui/icons-material/esm/Visibility.js
var import_jsx_runtime52 = __toESM(require_jsx_runtime());
var Visibility_default = createSvgIcon((0, import_jsx_runtime52.jsx)("path", {
  d: "M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5M12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5m0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3"
}), "Visibility");

// node_modules/@mui/icons-material/esm/VisibilityOff.js
var import_jsx_runtime53 = __toESM(require_jsx_runtime());
var VisibilityOff_default = createSvgIcon((0, import_jsx_runtime53.jsx)("path", {
  d: "M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7M2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2m4.31-.78 3.15 3.15.02-.16c0-1.66-1.34-3-3-3z"
}), "VisibilityOff");

// node_modules/@mui/icons-material/esm/WarningAmber.js
var import_jsx_runtime54 = __toESM(require_jsx_runtime());
var WarningAmber_default = createSvgIcon([(0, import_jsx_runtime54.jsx)("path", {
  d: "M12 5.99 19.53 19H4.47zM12 2 1 21h22z"
}, "0"), (0, import_jsx_runtime54.jsx)("path", {
  d: "M13 16h-2v2h2zm0-6h-2v5h2z"
}, "1")], "WarningAmber");

export {
  AccountCircle_default,
  Add_default,
  AddCircleOutline_default,
  ArrowCircleDown_default,
  ArrowCircleUp_default,
  ArrowDropDown_default,
  BookmarkAdd_default,
  BookmarkBorder_default,
  BookmarkRemove_default,
  Brightness4_default,
  Brightness7_default,
  CancelOutlined_default,
  CheckBox_default,
  CheckBoxOutlineBlank_default,
  CheckCircle_default,
  Clear_default,
  Create_default,
  Dashboard_default,
  Delete_default,
  DeleteOutline_default,
  Done_default,
  DragIndicator_default,
  Error_default,
  ErrorOutline_default,
  FilterList_default,
  GetApp_default,
  HelpOutline_default,
  HighlightOff_default,
  History_default,
  HotTub_default,
  Inbox_default,
  List_default,
  Lock_default,
  Menu_default,
  NavigateBefore_default,
  NavigateNext_default,
  PowerSettingsNew_default,
  Queue_default,
  Refresh_default,
  RemoveCircle_default,
  RemoveCircleOutline_default,
  RemoveRedEye_default,
  Report_default,
  Save_default,
  Search_default,
  Settings_default,
  Sort_default,
  Translate_default,
  Update_default,
  ViewList_default,
  ViewWeek_default,
  Visibility_default,
  VisibilityOff_default,
  WarningAmber_default
};
//# sourceMappingURL=chunk-VWH625ZS.js.map
