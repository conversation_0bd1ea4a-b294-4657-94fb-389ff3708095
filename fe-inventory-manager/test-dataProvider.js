// Simple test script to verify data provider functionality
import dataProvider from './src/dataProvider/rest.js';

// Test the data provider
async function testDataProvider() {
    try {
        console.log('Testing data provider...');
        
        // Test getList
        const result = await dataProvider.getList('products', {
            pagination: { page: 1, perPage: 10 },
            sort: { field: 'id', order: 'ASC' },
            filter: {}
        });
        
        console.log('getList result:', result);
        console.log('Data provider is working!');
    } catch (error) {
        console.error('Data provider error:', error);
    }
}

testDataProvider();
