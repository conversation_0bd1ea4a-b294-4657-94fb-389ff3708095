<!DOCTYPE html>
<html>
<head>
    <title>Data Provider Integration Test</title>
</head>
<body>
    <h1>Testing Frontend-Backend Integration</h1>
    <div id="results"></div>
    
    <script>
        async function testIntegration() {
            const results = document.getElementById('results');
            
            try {
                // Test 1: Login
                results.innerHTML += '<h2>Test 1: Login</h2>';
                const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'testuser2',
                        password: 'Test123!'
                    })
                });
                
                if (!loginResponse.ok) {
                    throw new Error('Login failed');
                }
                
                const loginData = await loginResponse.json();
                results.innerHTML += '<p>✅ Login successful</p>';
                
                // Test 2: Fetch products with token
                results.innerHTML += '<h2>Test 2: Fetch Products</h2>';
                const productsResponse = await fetch('http://localhost:3000/api/products', {
                    headers: {
                        'Authorization': `Bearer ${loginData.token}`
                    }
                });
                
                if (!productsResponse.ok) {
                    throw new Error('Products fetch failed');
                }
                
                const productsData = await productsResponse.json();
                results.innerHTML += '<p>✅ Products fetch successful</p>';
                results.innerHTML += `<p>Found ${productsData.products?.length || 0} products</p>`;
                
                // Test 3: Test CORS
                results.innerHTML += '<h2>Test 3: CORS</h2>';
                results.innerHTML += '<p>✅ CORS is working (no errors above)</p>';
                
                results.innerHTML += '<h2>✅ All tests passed! Integration is working.</h2>';
                
            } catch (error) {
                results.innerHTML += `<h2>❌ Test failed: ${error.message}</h2>`;
                console.error('Integration test error:', error);
            }
        }
        
        // Run tests when page loads
        testIntegration();
    </script>
</body>
</html>
